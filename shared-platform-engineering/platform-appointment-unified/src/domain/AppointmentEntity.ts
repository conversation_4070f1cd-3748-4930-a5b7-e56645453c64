/**
 * 🎯 APPOINTMENT ENTITY - Domain-Driven Design Entity
 *
 * Following <PERSON><PERSON><PERSON>'s approach with proper DDD patterns,
 * BaseEntity inheritance, and class-validator decorators.
 */

import 'reflect-metadata';
import {
  IsString,
  IsEmail,
  IsOptional,
  IsEnum,
  IsDate,
  IsNumber,
  IsPositive,
  IsInt,
  MinLength,
  IsUrl,
} from 'class-validator';
import { Type } from 'class-transformer';
import {
  BaseEntity,
  type UniqueEntityID,
  type IBaseEntityProps,
  EntityFactory,
} from '@beauty-crm/product-domain-types';
import type { AppointmentStatus } from '../schema/appointment-schema';

// ============================================================================
// 🎯 APPOINTMENT PROPS INTERFACE
// ============================================================================

export interface IAppointmentProps extends IBaseEntityProps {
  // Core identifiers
  salonId: string;
  customerId: string;
  staffId?: string;
  treatmentId: string;

  // Customer Info (denormalized for performance)
  customerName: string;
  customerEmail: string;
  customerPhone?: string;

  // Treatment Info (denormalized for performance)
  treatmentName: string;
  treatmentDuration: number;
  treatmentPrice: number;

  // Salon Info (denormalized for performance)
  salonName: string;
  salonLogo?: string;
  salonColor?: string;

  // Scheduling
  startTime: Date;
  endTime: Date;

  // Status & Metadata
  status: AppointmentStatus;
  notes?: string;
  locale: string;

  // Source tracking
  source: 'planner' | 'management' | 'api' | 'import';
  plannerAppointmentId?: string;

  // Timestamps (optional in pr?? new Date();
    this.props = { ...props }
completedAt?: Date;
cancelledAt?: Date;
this.validate();
}

  // ========MENT STATUS ENUM
// ============================================================================
// =========================================

  get salonId(): string
{
  return this.p
  (CONFIRMED = 'CONFIRMED'), turn;
  this.props.customerId;
}
get;
staffId();
: string | undefined
{
  return this.props.staffId;
}
get;
treatmentId();
: string
{
  return this.props.treatmentId;
}

get;
customerName();
: string
{
  return this.props.customerName;
}
get;
customerEmail();
: string
{
  return this.props.customerEmail;
}
get;
customerPhone();
: string | undefin
}
  get treatmentName(): strtName
}
  get treatmentDuration(): number
{
  return this.props.
  PLANNER = 'planner',
  MANAGEMENT = 'management',
  API = 'api',
  IMPORT = 'import',
}

// ============================================================================
// 🔥 APPOINTMENT ENTITY
// ============================================================================

export class AppointmentEntity extends BaseEntity<IAppointmentProps> {
  // Core identifiers with validation
  @IsString()
  private readonly _salonId: string;

  @IsString()
  private readonly _customerId: string;

  @IsString()
  @IsOptional()
  private readonly _staffId?: string;

  @IsString()
  private readonly _treatmentId: string;

  // Customer Info with validation
  @IsString()
  @MinLength(1)
  private _customerName: string;

  @IsEmail()
  private _customerEmail: string;

  @IsString()
  @IsOptional()
  private _customerPhone?: string;

  // Treatment Info with validation
  @IsString()
  @MinLength(1)
  private _treatmentName: string;

  @IsInt()
  @IsPositive()
  private _treatmentDuration: number;

  @IsNumber()
  @IsPositive()
  private _treatmentPrice: number;

  // Salon Info with validation
  @IsString()
  @MinLength(1)
  private _salonName: string;

  @IsUrl()
  @IsOptional()
  private _salonLogo?: string;

  @IsString()
  @IsOptional()
  private _salonColor?: string;

  // Scheduling with validation
  @IsDate()
  @Type(() => Date)
  private _startTime: Date;

  @IsDate()
  @Type(() => Date)
  private _endTime: Date;

  // Status & Metadata with validation
  @IsEnum(AppointmentStatusEnum)
  private _status: AppointmentStatusEnum;

  @IsString()
  @IsOptional()
  private _notes?: string;

  @IsString()
  private _locale: string;

  // Source tracking with validation
  @IsEnum(AppointmentSourceEnum)
  private _source: AppointmentSourceEnum;

  @IsString()
  @IsOptional()
  private _plannerAppointmentId?: string;

  // Additional timestamps
  @IsDate()
  @Type(() => Date)
  @IsOptional()
  private _confirmedAt?: Date;

  @IsDate()
  @Type(() => Date)
  @IsOptional()
  private _completedAt?: Date;

  @IsDate()
  @Type(() => Date)
  @IsOptional()
  private _cancelledAt?: Date;

  constructor(props: IAppointmentProps, id?: UniqueEntityID) {
    super(props, id);

    // Initialize private fields
    this._salonId = props.salonId;
    this._customerId = props.customerId;
    this._staffId = props.staffId;
    this._treatmentId = props.treatmentId;
    this._customerName = props.customerName;
    this._customerEmail = props.customerEmail;
    this._customerPhone = props.customerPhone;
    this._treatmentName = props.treatmentName;
    this._treatmentDuration = props.treatmentDuration;
    this._treatmentPrice = props.treatmentPrice;
    this._salonName = props.salonName;
    this._salonLogo = props.salonLogo;
    this._salonColor = props.salonColor;
    this._startTime = props.startTime;
    this._endTime = props.endTime;
    this._status = props.status as AppointmentStatusEnum;
    this._notes = props.notes;
    this._locale = props.locale || 'en-US';
    this._source = props.source as AppointmentSourceEnum;
    this._plannerAppointmentId = props.plannerAppointmentId;
    this._confirmedAt = props.confirmedAt;
    this._completedAt = props.completedAt;
    this._cancelledAt = props.cancelledAt;
  }

  // ============================================================================
  // 🎯 GETTERS
  // ============================================================================

  get salonId(): string {
    return this._salonId;
  }
  get customerId(): string {
    return this._customerId;
  }
  get staffId(): string | undefined {
    return this._staffId;
  }
  get treatmentId(): string {
    return this._treatmentId;
  }
  get customerName(): string {
    return this._customerName;
  }
  get customerEmail(): string {
    return this._customerEmail;
  }
  get customerPhone(): string | undefined {
    return this._customerPhone;
  }
  get treatmentName(): string {
    return this._treatmentName;
  }
  get treatmentDuration(): number {
    return this._treatmentDuration;
  }
  get treatmentPrice(): number {
    return this._treatmentPrice;
  }
  get salonName(): string {
    return this._salonName;
  }
  get salonLogo(): string | undefined {
    return this._salonLogo;
  }
  get salonColor(): string | undefined {
    return this._salonColor;
  }
  get startTime(): Date {
    return this._startTime;
  }
  get endTime(): Date {
    return this._endTime;
  }
  get status(): AppointmentStatusEnum {
    return this._status;
  }
  get notes(): string | undefined {
    return this._notes;
  }
  get locale(): string {
    return this._locale;
  }
  get source(): AppointmentSourceEnum {
    return this._source;
  }
  get plannerAppointmentId(): string | undefined {
    return this._plannerAppointmentId;
  }
  get confirmedAt(): Date | undefined {
    return this._confirmedAt;
  }
  get completedAt(): Date | undefined {
    return this._completedAt;
  }
  get cancelledAt(): Date | undefined {
    return this._cancelledAt;
  }

  // ============================================================================
  // 🎯 BUSINESS LOGIC METHODS
  // ============================================================================

  /**
   * Confirms the appointment
   */
  public confirm(): void {
    if (this._status === AppointmentStatusEnum.CANCELLED) {
      throw new Error('Cannot confirm a cancelled appointment');
    }
    this._status = AppointmentStatusEnum.CONFIRMED;
    this._confirmedAt = new Date();
    this.markAsUpdated();
  }

  /**
   * Cancels the appointment
   */
  public cancel(reason?: string): void {
    if (this._status === AppointmentStatusEnum.COMPLETED) {
      throw new Error('Cannot cancel a completed appointment');
    }
    this._status = AppointmentStatusEnum.CANCELLED;
    this._cancelledAt = new Date();
    if (reason) {
      this._notes = this._notes
        ? `${this._notes}\nCancellation reason: ${reason}`
        : `Cancellation reason: ${reason}`;
    }
    this.markAsUpdated();
  }

  /**
   * Completes the appointment
   */
  public complete(): void {
    if (
      this._status !== AppointmentStatusEnum.IN_PROGRESS &&
      this._status !== AppointmentStatusEnum.CONFIRMED
    ) {
      throw new Error(
        'Can only complete appointments that are in progress or confirmed',
      );
    }
    this._status = AppointmentStatusEnum.COMPLETED;
    this._completedAt = new Date();
    this.markAsUpdated();
  }

  /**
   * Reschedules the appointment
   */
  public reschedule(newStartTime: Date, newEndTime: Date): void {
    if (!this.canBeRescheduled()) {
      throw new Error('Appointment cannot be rescheduled in its current state');
    }
    this._startTime = newStartTime;
    this._endTime = newEndTime;
    this._status = AppointmentStatusEnum.RESCHEDULED;
    this.markAsUpdated();
  }

  /**
   * Marks appointment as no-show
   */
  public markNoShow(): void {
    if (this._status === AppointmentStatusEnum.COMPLETED) {
      throw new Error('Cannot mark completed appointment as no-show');
    }
    this._status = AppointmentStatusEnum.NO_SHOW;
    this.markAsUpdated();
  }

  /**
   * Starts the appointment (sets to in progress)
   */
  public start(): void {
    if (this._status !== AppointmentStatusEnum.CONFIRMED) {
      throw new Error('Can only start confirmed appointments');
    }
    this._status = AppointmentStatusEnum.IN_PROGRESS;
    this.markAsUpdated();
  }

  /**
   * Updates customer information
   */
  public updateCustomerInfo(
    customerName?: string,
    customerEmail?: string,
    customerPhone?: string,
  ): void {
    if (customerName) this._customerName = customerName;
    if (customerEmail) this._customerEmail = customerEmail;
    if (customerPhone !== undefined) this._customerPhone = customerPhone;
    this.markAsUpdated();
  }

  /**
   * Updates notes
   */
  public updateNotes(notes: string): void {
    this._notes = notes;
    this.markAsUpdated();
  }

  // ============================================================================
  // 🎯 BUSINESS RULES & VALIDATION
  // ============================================================================

  /**
   * Checks if appointment is in the past
   */
  public isPast(): boolean {
    return this._endTime < new Date();
  }
}
