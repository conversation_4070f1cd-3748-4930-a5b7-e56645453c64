/**
 * 🔄 UNIFIED APPOINTMENT TYPES - Cross-System Compatibility
 *
 * These types ensure compatibility between different appointment systems
 * (planner, management, etc.) while maintaining the single source of truth.
 */

import { z } from 'zod';
import type { BaseEntity } from '@beauty-crm/product-domain-types';
import type { Appointment } from '../schema/appointment-schema';

// ============================================================================
// 🔥 UNIFIED STATUS ENUM
// ============================================================================

export enum UnifiedAppointmentStatus {
  PENDING = 'PENDING',
  CONFIRMED = 'CONFIRMED',
  SCHEDULED = 'SCHEDULED',
  IN_PROGRESS = 'IN_PROGRESS',
  COMPLETED = 'COMPLETED',
  CANCELLED = 'CANCELLED',
  NO_SHOW = 'NO_SHOW',
  RESCHEDULED = 'RESCHEDULED',
}

// ============================================================================
// 🔄 UNIFIED APPOINTMENT INTERFACE
// ============================================================================

export interface UnifiedAppointment {
  // Core identifiers
  id: string;
  externalId?: string; // For cross-system mapping

  // Customer/Client data
  customerId: string;
  customerName: string;
  customerEmail: string;
  customerPhone?: string;

  // Salon/Staff data
  salonId: string;
  salonName: string;
  staffId?: string;
  staffName?: string;

  // Service data
  treatmentId: string;
  treatmentName: string;
  treatmentDuration: number;
  treatmentPrice: number;

  // Scheduling
  startTime: Date;
  endTime: Date;
  timezone?: string;

  // Status & metadata
  status: UnifiedAppointmentStatus;
  source: 'planner' | 'management' | 'api' | 'import';
  notes?: string;
  locale?: string;

  // Cross-system reference
  plannerAppointmentId?: string;

  // Timestamps
  createdAt: Date;
  updatedAt: Date;
  confirmedAt?: Date;
  completedAt?: Date;
  cancelledAt?: Date;
}

// ============================================================================
// 🎯 MANAGEMENT VIEW TYPES
// ============================================================================

export interface AppointmentManagementView {
  id: string;
  customerName: string;
  customerEmail: string;
  customerPhone?: string;
  treatmentName: string;
  staffName?: string;
  startTime: Date;
  endTime: Date;
  status: UnifiedAppointmentStatus;
  duration: number;
  price: number;
  notes?: string;
  canCancel: boolean;
  canReschedule: boolean;
  canComplete: boolean;
}

export interface AppointmentListItem {
  id: string;
  customerName: string;
  treatmentName: string;
  startTime: Date;
  duration: number;
  status: UnifiedAppointmentStatus;
  staffName?: string;
  price: number;
}

// ============================================================================
// 🔄 CONVERSION UTILITIES
// ============================================================================

/**
 * Converts core appointment to unified appointment
 */
export function toUnifiedAppointment(
  appointment: Appointment,
): UnifiedAppointment {
  return {
    id: appointment.id,
    customerId: appointment.customerId,
    customerName: appointment.customerName,
    customerEmail: appointment.customerEmail,
    customerPhone: appointment.customerPhone,
    salonId: appointment.salonId,
    salonName: appointment.salonName,
    staffId: appointment.staffId,
    treatmentId: appointment.treatmentId,
    treatmentName: appointment.treatmentName,
    treatmentDuration: appointment.treatmentDuration,
    treatmentPrice: appointment.treatmentPrice,
    startTime: appointment.startTime,
    endTime: appointment.endTime,
    status: appointment.status as UnifiedAppointmentStatus,
    source: appointment.source,
    notes: appointment.notes,
    locale: appointment.locale,
    plannerAppointmentId: appointment.plannerAppointmentId,
    createdAt: appointment.createdAt,
    updatedAt: appointment.updatedAt,
    confirmedAt: appointment.confirmedAt,
    completedAt: appointment.completedAt,
    cancelledAt: appointment.cancelledAt,
  };
}

/**
 * Converts unified appointment to core appointment
 */
export function fromUnifiedAppointment(
  unified: UnifiedAppointment,
): Appointment {
  return {
    id: unified.id,
    salonId: unified.salonId,
    customerId: unified.customerId,
    staffId: unified.staffId,
    treatmentId: unified.treatmentId,
    customerName: unified.customerName,
    customerEmail: unified.customerEmail,
    customerPhone: unified.customerPhone,
    treatmentName: unified.treatmentName,
    treatmentDuration: unified.treatmentDuration,
    treatmentPrice: unified.treatmentPrice,
    salonName: unified.salonName,
    salonLogo: undefined,
    salonColor: undefined,
    startTime: unified.startTime,
    endTime: unified.endTime,
    status: unified.status as Appointment['status'],
    notes: unified.notes,
    locale: unified.locale || 'en-US',
    source: unified.source,
    plannerAppointmentId: unified.plannerAppointmentId,
    createdAt: unified.createdAt,
    updatedAt: unified.updatedAt,
    confirmedAt: unified.confirmedAt,
    completedAt: unified.completedAt,
    cancelledAt: unified.cancelledAt,
  };
}

// ============================================================================
// 🔄 STATUS CONVERSION UTILITIES
// ============================================================================

/**
 * Converts to Prisma status string
 */
export function toPrismaStatus(status: UnifiedAppointmentStatus): string {
  return status.toString();
}

/**
 * Converts from Prisma status string
 */
export function fromPrismaStatus(status: string): UnifiedAppointmentStatus {
  return status as UnifiedAppointmentStatus;
}

/**
 * Converts to unified status
 */
export function toUnifiedStatus(status: string): UnifiedAppointmentStatus {
  return status as UnifiedAppointmentStatus;
}

/**
 * Converts from unified status
 */
export function fromUnifiedStatus(status: UnifiedAppointmentStatus): string {
  return status.toString();
}

// ============================================================================
// 🎯 VIEW CONVERSION UTILITIES
// ============================================================================

/**
 * Converts appointment to management view
 */
export function toManagementView(
  appointment: Appointment,
): AppointmentManagementView {
  return {
    id: appointment.id,
    customerName: appointment.customerName,
    customerEmail: appointment.customerEmail,
    customerPhone: appointment.customerPhone,
    treatmentName: appointment.treatmentName,
    staffName: undefined, // Would be populated by service layer
    startTime: appointment.startTime,
    endTime: appointment.endTime,
    status: appointment.status as UnifiedAppointmentStatus,
    duration: appointment.treatmentDuration,
    price: appointment.treatmentPrice,
    notes: appointment.notes,
    canCancel: !['COMPLETED', 'CANCELLED', 'NO_SHOW'].includes(
      appointment.status,
    ),
    canReschedule: ['PENDING', 'CONFIRMED', 'SCHEDULED'].includes(
      appointment.status,
    ),
    canComplete: ['CONFIRMED', 'IN_PROGRESS'].includes(appointment.status),
  };
}

/**
 * Converts appointment to list item
 */
export function toListItem(appointment: Appointment): AppointmentListItem {
  return {
    id: appointment.id,
    customerName: appointment.customerName,
    treatmentName: appointment.treatmentName,
    startTime: appointment.startTime,
    duration: appointment.treatmentDuration,
    status: appointment.status as UnifiedAppointmentStatus,
    staffName: undefined, // Would be populated by service layer
    price: appointment.treatmentPrice,
  };
}

// ============================================================================
// 🔥 ZOD SCHEMAS
// ============================================================================

export const UnifiedAppointmentStatusSchema = z.nativeEnum(
  UnifiedAppointmentStatus,
);

export const UnifiedAppointmentSchema = z.object({
  id: z.string(),
  externalId: z.string().optional(),
  customerId: z.string(),
  customerName: z.string(),
  customerEmail: z.string().email(),
  customerPhone: z.string().optional(),
  salonId: z.string(),
  salonName: z.string(),
  staffId: z.string().optional(),
  staffName: z.string().optional(),
  treatmentId: z.string(),
  treatmentName: z.string(),
  treatmentDuration: z.number().positive(),
  treatmentPrice: z.number().positive(),
  startTime: z.date(),
  endTime: z.date(),
  timezone: z.string().optional(),
  status: UnifiedAppointmentStatusSchema,
  source: z.enum(['planner', 'management', 'api', 'import']),
  notes: z.string().optional(),
  locale: z.string().optional(),
  plannerAppointmentId: z.string().optional(),
  createdAt: z.date(),
  updatedAt: z.date(),
  confirmedAt: z.date().optional(),
  completedAt: z.date().optional(),
  cancelledAt: z.date().optional(),
});

export const AppointmentManagementViewSchema = z.object({
  id: z.string(),
  customerName: z.string(),
  customerEmail: z.string().email(),
  customerPhone: z.string().optional(),
  treatmentName: z.string(),
  staffName: z.string().optional(),
  startTime: z.date(),
  endTime: z.date(),
  status: UnifiedAppointmentStatusSchema,
  duration: z.number().positive(),
  price: z.number().positive(),
  notes: z.string().optional(),
  canCancel: z.boolean(),
  canReschedule: z.boolean(),
  canComplete: z.boolean(),
});

export const AppointmentListItemSchema = z.object({
  id: z.string(),
  customerName: z.string(),
  treatmentName: z.string(),
  startTime: z.date(),
  duration: z.number().positive(),
  status: UnifiedAppointmentStatusSchema,
  staffName: z.string().optional(),
  price: z.number().positive(),
});
