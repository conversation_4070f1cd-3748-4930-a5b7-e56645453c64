/**
 * 📡 EVENT TYPES - Cross-System Event Compatibility
 *
 * These types ensure event compatibility between different appointment systems.
 */

// ============================================================================
// 🔥 EVENT TYPE CONSTANTS
// ============================================================================

export const AppointmentEventTypes = {
  CREATED: 'appointment.created',
  UPDATED: 'appointment.updated',
  CANCELLED: 'appointment.cancelled',
  CONFIRMED: 'appointment.confirmed',
  COMPLETED: 'appointment.completed',
  RESCHEDULED: 'appointment.rescheduled',
  NO_SHOW: 'appointment.no_show',
} as const;

export type AppointmentEventType =
  (typeof AppointmentEventTypes)[keyof typeof AppointmentEventTypes];

// ============================================================================
// 🎯 EVENT PAYLOAD TYPES
// ============================================================================

export interface AppointmentEventPayload {
  appointmentId: string;
  eventType: AppointmentEventType;
  timestamp: Date;
  source: string;
  correlationId?: string;
  userId?: string;
  metadata?: Record<string, unknown>;
}

export interface AppointmentCreatedPayload extends AppointmentEventPayload {
  eventType: typeof AppointmentEventTypes.CREATED;
  data: {
    appointment: any; // Would be the full appointment object
  };
}

export interface AppointmentUpdatedPayload extends AppointmentEventPayload {
  eventType: typeof AppointmentEventTypes.UPDATED;
  data: {
    appointment: any;
    changes: Record<string, unknown>;
    previousValues?: Record<string, unknown>;
  };
}

export interface AppointmentCancelledPayload extends AppointmentEventPayload {
  eventType: typeof AppointmentEventTypes.CANCELLED;
  data: {
    reason?: string;
    cancelledBy?: string;
    refundAmount?: number;
  };
}

export interface AppointmentConfirmedPayload extends AppointmentEventPayload {
  eventType: typeof AppointmentEventTypes.CONFIRMED;
  data: {
    confirmedBy?: string;
    confirmationMethod?: 'email' | 'sms' | 'phone' | 'in_person';
  };
}

export interface AppointmentCompletedPayload extends AppointmentEventPayload {
  eventType: typeof AppointmentEventTypes.COMPLETED;
  data: {
    completedBy?: string;
    actualDuration?: number;
    customerSatisfaction?: number;
    notes?: string;
  };
}

export interface AppointmentRescheduledPayload extends AppointmentEventPayload {
  eventType: typeof AppointmentEventTypes.RESCHEDULED;
  data: {
    previousStartTime: Date;
    previousEndTime: Date;
    newStartTime: Date;
    newEndTime: Date;
    reason?: string;
    rescheduledBy?: string;
  };
}

export interface AppointmentNoShowPayload extends AppointmentEventPayload {
  eventType: typeof AppointmentEventTypes.NO_SHOW;
  data: {
    waitTime?: number;
    noShowFee?: number;
    notificationsSent?: number;
  };
}

// ============================================================================
// 🎯 UNION TYPES
// ============================================================================

export type AppointmentEventPayloadUnion =
  | AppointmentCreatedPayload
  | AppointmentUpdatedPayload
  | AppointmentCancelledPayload
  | AppointmentConfirmedPayload
  | AppointmentCompletedPayload
  | AppointmentRescheduledPayload
  | AppointmentNoShowPayload;

// ============================================================================
// 🔄 EVENT HANDLER TYPES
// ============================================================================

export type AppointmentEventHandler<
  T extends AppointmentEventPayload = AppointmentEventPayload,
> = (event: T) => Promise<void> | void;

export interface AppointmentEventHandlers {
  [AppointmentEventTypes.CREATED]?: AppointmentEventHandler<AppointmentCreatedPayload>;
  [AppointmentEventTypes.UPDATED]?: AppointmentEventHandler<AppointmentUpdatedPayload>;
  [AppointmentEventTypes.CANCELLED]?: AppointmentEventHandler<AppointmentCancelledPayload>;
  [AppointmentEventTypes.CONFIRMED]?: AppointmentEventHandler<AppointmentConfirmedPayload>;
  [AppointmentEventTypes.COMPLETED]?: AppointmentEventHandler<AppointmentCompletedPayload>;
  [AppointmentEventTypes.RESCHEDULED]?: AppointmentEventHandler<AppointmentRescheduledPayload>;
  [AppointmentEventTypes.NO_SHOW]?: AppointmentEventHandler<AppointmentNoShowPayload>;
}

// ============================================================================
// 🎯 EVENT UTILITIES
// ============================================================================

export function isAppointmentEvent(
  event: unknown,
): event is AppointmentEventPayload {
  return (
    typeof event === 'object' &&
    event !== null &&
    'appointmentId' in event &&
    'eventType' in event &&
    'timestamp' in event &&
    'source' in event
  );
}

export function getEventTypeName(eventType: AppointmentEventType): string {
  const typeMap = {
    [AppointmentEventTypes.CREATED]: 'Created',
    [AppointmentEventTypes.UPDATED]: 'Updated',
    [AppointmentEventTypes.CANCELLED]: 'Cancelled',
    [AppointmentEventTypes.CONFIRMED]: 'Confirmed',
    [AppointmentEventTypes.COMPLETED]: 'Completed',
    [AppointmentEventTypes.RESCHEDULED]: 'Rescheduled',
    [AppointmentEventTypes.NO_SHOW]: 'No Show',
  };

  return typeMap[eventType] || 'Unknown';
}
