/**
 * 📡 APPOINTMENT EVENTS - Single Source of Truth for Event Schemas
 *
 * All appointment events derive from the core schema.
 * No more duplicate event definitions across services!
 */

import { z } from 'zod';
import {
  AppointmentCoreSchema,
  type Appointment,
} from '../schema/appointment-schema';

// ============================================================================
// 🎯 BASE EVENT SCHEMA
// ============================================================================

export const BaseEventSchema = z.object({
  eventId: z.string().cuid(),
  eventType: z.string(),
  aggregateId: z.string().cuid(),
  aggregateType: z.literal('appointment'),
  timestamp: z.date(),
  eventVersion: z.number().int().positive(),
  source: z.string(),
  correlationId: z.string().optional(),
  userId: z.string().optional(),
});

// ============================================================================
// 🔥 APPOINTMENT EVENT TYPES
// ============================================================================

export const AppointmentCreatedEventSchema = BaseEventSchema.extend({
  eventType: z.literal('appointment.created'),
  data: z.object({
    appointment: AppointmentCoreSchema,
  }),
});

export const AppointmentUpdatedEventSchema = BaseEventSchema.extend({
  eventType: z.literal('appointment.updated'),
  data: z.object({
    appointment: AppointmentCoreSchema,
    changes: AppointmentCoreSchema.partial(),
    previousValues: AppointmentCoreSchema.partial().optional(),
  }),
});

export const AppointmentCancelledEventSchema = BaseEventSchema.extend({
  eventType: z.literal('appointment.cancelled'),
  data: z.object({
    appointmentId: z.string().cuid(),
    reason: z.string().optional(),
    cancelledBy: z.string().optional(),
    refundAmount: z.number().optional(),
  }),
});

export const AppointmentConfirmedEventSchema = BaseEventSchema.extend({
  eventType: z.literal('appointment.confirmed'),
  data: z.object({
    appointmentId: z.string().cuid(),
    confirmedBy: z.string().optional(),
    confirmationMethod: z
      .enum(['email', 'sms', 'phone', 'in_person'])
      .optional(),
  }),
});

export const AppointmentCompletedEventSchema = BaseEventSchema.extend({
  eventType: z.literal('appointment.completed'),
  data: z.object({
    appointmentId: z.string().cuid(),
    completedBy: z.string().optional(),
    actualDuration: z.number().int().positive().optional(),
    customerSatisfaction: z.number().min(1).max(5).optional(),
    notes: z.string().optional(),
  }),
});

export const AppointmentRescheduledEventSchema = BaseEventSchema.extend({
  eventType: z.literal('appointment.rescheduled'),
  data: z.object({
    appointmentId: z.string().cuid(),
    previousStartTime: z.date(),
    previousEndTime: z.date(),
    newStartTime: z.date(),
    newEndTime: z.date(),
    reason: z.string().optional(),
    rescheduledBy: z.string().optional(),
  }),
});

export const AppointmentNoShowEventSchema = BaseEventSchema.extend({
  eventType: z.literal('appointment.no_show'),
  data: z.object({
    appointmentId: z.string().cuid(),
    waitTime: z.number().int().positive().optional(), // minutes waited
    noShowFee: z.number().optional(),
    notificationsSent: z.number().int().default(0),
  }),
});

// ============================================================================
// 🎯 UNION EVENT SCHEMA
// ============================================================================

export const AppointmentEventSchema = z.discriminatedUnion('eventType', [
  AppointmentCreatedEventSchema,
  AppointmentUpdatedEventSchema,
  AppointmentCancelledEventSchema,
  AppointmentConfirmedEventSchema,
  AppointmentCompletedEventSchema,
  AppointmentRescheduledEventSchema,
  AppointmentNoShowEventSchema,
]);

// ============================================================================
// 🔥 TYPE EXPORTS
// ============================================================================

export type BaseEvent = z.infer<typeof BaseEventSchema>;
export type AppointmentCreatedEvent = z.infer<
  typeof AppointmentCreatedEventSchema
>;
export type AppointmentUpdatedEvent = z.infer<
  typeof AppointmentUpdatedEventSchema
>;
export type AppointmentCancelledEvent = z.infer<
  typeof AppointmentCancelledEventSchema
>;
export type AppointmentConfirmedEvent = z.infer<
  typeof AppointmentConfirmedEventSchema
>;
export type AppointmentCompletedEvent = z.infer<
  typeof AppointmentCompletedEventSchema
>;
export type AppointmentRescheduledEvent = z.infer<
  typeof AppointmentRescheduledEventSchema
>;
export type AppointmentNoShowEvent = z.infer<
  typeof AppointmentNoShowEventSchema
>;
export type AppointmentEvent = z.infer<typeof AppointmentEventSchema>;

// ============================================================================
// 🎯 EVENT CREATORS
// ============================================================================

export const createAppointmentCreatedEvent = (
  appointment: Appointment,
  options: {
    source: string;
    correlationId?: string;
    userId?: string;
  },
): AppointmentCreatedEvent => {
  return {
    eventId: crypto.randomUUID(),
    eventType: 'appointment.created',
    aggregateId: appointment.id,
    aggregateType: 'appointment',
    timestamp: new Date(),
    eventVersion: 1,
    source: options.source,
    correlationId: options.correlationId,
    userId: options.userId,
    data: {
      appointment,
    },
  };
};

export const createAppointmentUpdatedEvent = (
  appointment: Appointment,
  changes: Partial<Appointment>,
  options: {
    source: string;
    correlationId?: string;
    userId?: string;
    previousValues?: Partial<Appointment>;
  },
): AppointmentUpdatedEvent => {
  return {
    eventId: crypto.randomUUID(),
    eventType: 'appointment.updated',
    aggregateId: appointment.id,
    aggregateType: 'appointment',
    timestamp: new Date(),
    eventVersion: 1,
    source: options.source,
    correlationId: options.correlationId,
    userId: options.userId,
    data: {
      appointment,
      changes,
      previousValues: options.previousValues,
    },
  };
};

export const createAppointmentCancelledEvent = (
  appointmentId: string,
  options: {
    source: string;
    reason?: string;
    cancelledBy?: string;
    refundAmount?: number;
    correlationId?: string;
    userId?: string;
  },
): AppointmentCancelledEvent => {
  return {
    eventId: crypto.randomUUID(),
    eventType: 'appointment.cancelled',
    aggregateId: appointmentId,
    aggregateType: 'appointment',
    timestamp: new Date(),
    eventVersion: 1,
    source: options.source,
    correlationId: options.correlationId,
    userId: options.userId,
    data: {
      appointmentId,
      reason: options.reason,
      cancelledBy: options.cancelledBy,
      refundAmount: options.refundAmount,
    },
  };
};

export const createAppointmentConfirmedEvent = (
  appointmentId: string,
  options: {
    source: string;
    confirmedBy?: string;
    confirmationMethod?: 'email' | 'sms' | 'phone' | 'in_person';
    correlationId?: string;
    userId?: string;
  },
): AppointmentConfirmedEvent => {
  return {
    eventId: crypto.randomUUID(),
    eventType: 'appointment.confirmed',
    aggregateId: appointmentId,
    aggregateType: 'appointment',
    timestamp: new Date(),
    eventVersion: 1,
    source: options.source,
    correlationId: options.correlationId,
    userId: options.userId,
    data: {
      appointmentId,
      confirmedBy: options.confirmedBy,
      confirmationMethod: options.confirmationMethod,
    },
  };
};

export const createAppointmentCompletedEvent = (
  appointmentId: string,
  options: {
    source: string;
    completedBy?: string;
    actualDuration?: number;
    customerSatisfaction?: number;
    notes?: string;
    correlationId?: string;
    userId?: string;
  },
): AppointmentCompletedEvent => {
  return {
    eventId: crypto.randomUUID(),
    eventType: 'appointment.completed',
    aggregateId: appointmentId,
    aggregateType: 'appointment',
    timestamp: new Date(),
    eventVersion: 1,
    source: options.source,
    correlationId: options.correlationId,
    userId: options.userId,
    data: {
      appointmentId,
      completedBy: options.completedBy,
      actualDuration: options.actualDuration,
      customerSatisfaction: options.customerSatisfaction,
      notes: options.notes,
    },
  };
};
