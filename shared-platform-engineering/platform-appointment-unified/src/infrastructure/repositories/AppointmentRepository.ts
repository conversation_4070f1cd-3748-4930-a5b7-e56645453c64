/**
 * 🏗️ APPOINTMENT REPOSITORY - Unified Data Access Layer
 *
 * Provides a clean interface for appointment data operations with multiple implementations.
 */

import type { PrismaClient } from '@prisma/client';
import type {
  Appointment,
  CreateAppointmentRequest,
} from '../../schema/appointment-schema';
import {
  prismaToAppointment,
  appointmentToPrismaCreate,
} from '../../schema/adapters/prisma-adapter';

// ============================================================================
// 🎯 REPOSITORY INTERFACE
// ============================================================================

export interface AppointmentRepository {
  // Basic CRUD operations
  create(appointment: CreateAppointmentRequest): Promise<Appointment>;
  findById(id: string): Promise<Appointment | null>;
  findAll(): Promise<Appointment[]>;
  update(id: string, updates: Partial<Appointment>): Promise<Appointment>;
  delete(id: string): Promise<void>;

  // Query operations
  findByCustomerId(customerId: string): Promise<Appointment[]>;
  findByStaffId(staffId: string): Promise<Appointment[]>;
  findBySalonId(salonId: string): Promise<Appointment[]>;
  findByDateRange(startDate: Date, endDate: Date): Promise<Appointment[]>;
  findByStatus(status: string): Promise<Appointment[]>;

  // Business operations
  findConflictingAppointments(
    staffId: string,
    startTime: Date,
    endTime: Date,
    excludeId?: string,
  ): Promise<Appointment[]>;

  findAvailableSlots(
    salonId: string,
    staffId: string,
    date: Date,
    duration: number,
  ): Promise<{ start: Date; end: Date }[]>;
}

// ============================================================================
// 🔥 PRISMA IMPLEMENTATION
// ============================================================================

export class PrismaAppointmentRepository implements AppointmentRepository {
  constructor(private prisma: PrismaClient) {}

  async create(
    appointmentData: CreateAppointmentRequest,
  ): Promise<Appointment> {
    const prismaData = appointmentToPrismaCreate(appointmentData);
    const created = await this.prisma.appointment.create({
      data: prismaData,
    });
    return prismaToAppointment(created);
  }

  async findById(id: string): Promise<Appointment | null> {
    const appointment = await this.prisma.appointment.findUnique({
      where: { id },
    });
    return appointment ? prismaToAppointment(appointment) : null;
  }

  async findAll(): Promise<Appointment[]> {
    const appointments = await this.prisma.appointment.findMany({
      orderBy: { startTime: 'asc' },
    });
    return appointments.map(prismaToAppointment);
  }

  async update(
    id: string,
    updates: Partial<Appointment>,
  ): Promise<Appointment> {
    const updated = await this.prisma.appointment.update({
      where: { id },
      data: {
        ...updates,
        updatedAt: new Date(),
      },
    });
    return prismaToAppointment(updated);
  }

  async delete(id: string): Promise<void> {
    await this.prisma.appointment.delete({
      where: { id },
    });
  }

  async findByCustomerId(customerId: string): Promise<Appointment[]> {
    const appointments = await this.prisma.appointment.findMany({
      where: { customerId },
      orderBy: { startTime: 'desc' },
    });
    return appointments.map(prismaToAppointment);
  }

  async findByStaffId(staffId: string): Promise<Appointment[]> {
    const appointments = await this.prisma.appointment.findMany({
      where: { staffId },
      orderBy: { startTime: 'asc' },
    });
    return appointments.map(prismaToAppointment);
  }

  async findBySalonId(salonId: string): Promise<Appointment[]> {
    const appointments = await this.prisma.appointment.findMany({
      where: { salonId },
      orderBy: { startTime: 'asc' },
    });
    return appointments.map(prismaToAppointment);
  }

  async findByDateRange(
    startDate: Date,
    endDate: Date,
  ): Promise<Appointment[]> {
    const appointments = await this.prisma.appointment.findMany({
      where: {
        startTime: {
          gte: startDate,
          lte: endDate,
        },
      },
      orderBy: { startTime: 'asc' },
    });
    return appointments.map(prismaToAppointment);
  }

  async findByStatus(status: string): Promise<Appointment[]> {
    const appointments = await this.prisma.appointment.findMany({
      where: { status },
      orderBy: { startTime: 'asc' },
    });
    return appointments.map(prismaToAppointment);
  }

  async findConflictingAppointments(
    staffId: string,
    startTime: Date,
    endTime: Date,
    excludeId?: string,
  ): Promise<Appointment[]> {
    const appointments = await this.prisma.appointment.findMany({
      where: {
        staffId,
        id: excludeId ? { not: excludeId } : undefined,
        OR: [
          {
            AND: [
              { startTime: { lte: startTime } },
              { endTime: { gt: startTime } },
            ],
          },
          {
            AND: [
              { startTime: { lt: endTime } },
              { endTime: { gte: endTime } },
            ],
          },
          {
            AND: [
              { startTime: { gte: startTime } },
              { endTime: { lte: endTime } },
            ],
          },
        ],
      },
    });
    return appointments.map(prismaToAppointment);
  }

  async findAvailableSlots(
    salonId: string,
    staffId: string,
    date: Date,
    duration: number,
  ): Promise<{ start: Date; end: Date }[]> {
    // This is a simplified implementation
    // In reality, you'd also consider business hours, breaks, etc.
    const startOfDay = new Date(date);
    startOfDay.setHours(9, 0, 0, 0); // 9 AM

    const endOfDay = new Date(date);
    endOfDay.setHours(17, 0, 0, 0); // 5 PM

    const existingAppointments = await this.prisma.appointment.findMany({
      where: {
        salonId,
        staffId,
        startTime: {
          gte: startOfDay,
          lt: endOfDay,
        },
        status: {
          not: 'CANCELLED',
        },
      },
      orderBy: { startTime: 'asc' },
    });

    const slots: { start: Date; end: Date }[] = [];
    let currentTime = new Date(startOfDay);

    for (const appointment of existingAppointments) {
      // Check if there's a gap before this appointment
      const gapDuration =
        appointment.startTime.getTime() - currentTime.getTime();
      if (gapDuration >= duration * 60 * 1000) {
        slots.push({
          start: new Date(currentTime),
          end: new Date(currentTime.getTime() + duration * 60 * 1000),
        });
      }
      currentTime = new Date(appointment.endTime);
    }

    // Check for remaining time at end of day
    const remainingTime = endOfDay.getTime() - currentTime.getTime();
    if (remainingTime >= duration * 60 * 1000) {
      slots.push({
        start: new Date(currentTime),
        end: new Date(currentTime.getTime() + duration * 60 * 1000),
      });
    }

    return slots;
  }
}

// ============================================================================
// 🧪 IN-MEMORY IMPLEMENTATION (for testing)
// ============================================================================

export class InMemoryAppointmentRepository implements AppointmentRepository {
  private appointments: Appointment[] = [];
  private nextId = 1;

  async create(
    appointmentData: CreateAppointmentRequest,
  ): Promise<Appointment> {
    const appointment: Appointment = {
      ...appointmentData,
      id: `appointment_${this.nextId++}`,
      createdAt: new Date(),
      updatedAt: new Date(),
    };
    this.appointments.push(appointment);
    return appointment;
  }

  async findById(id: string): Promise<Appointment | null> {
    return this.appointments.find((a) => a.id === id) || null;
  }

  async findAll(): Promise<Appointment[]> {
    return [...this.appointments].sort(
      (a, b) => a.startTime.getTime() - b.startTime.getTime(),
    );
  }

  async update(
    id: string,
    updates: Partial<Appointment>,
  ): Promise<Appointment> {
    const index = this.appointments.findIndex((a) => a.id === id);
    if (index === -1) {
      throw new Error(`Appointment with id ${id} not found`);
    }

    this.appointments[index] = {
      ...this.appointments[index],
      ...updates,
      updatedAt: new Date(),
    };

    return this.appointments[index];
  }

  async delete(id: string): Promise<void> {
    const index = this.appointments.findIndex((a) => a.id === id);
    if (index === -1) {
      throw new Error(`Appointment with id ${id} not found`);
    }
    this.appointments.splice(index, 1);
  }

  async findByCustomerId(customerId: string): Promise<Appointment[]> {
    return this.appointments
      .filter((a) => a.customerId === customerId)
      .sort((a, b) => b.startTime.getTime() - a.startTime.getTime());
  }

  async findByStaffId(staffId: string): Promise<Appointment[]> {
    return this.appointments
      .filter((a) => a.staffId === staffId)
      .sort((a, b) => a.startTime.getTime() - b.startTime.getTime());
  }

  async findBySalonId(salonId: string): Promise<Appointment[]> {
    return this.appointments
      .filter((a) => a.salonId === salonId)
      .sort((a, b) => a.startTime.getTime() - b.startTime.getTime());
  }

  async findByDateRange(
    startDate: Date,
    endDate: Date,
  ): Promise<Appointment[]> {
    return this.appointments
      .filter((a) => a.startTime >= startDate && a.startTime <= endDate)
      .sort((a, b) => a.startTime.getTime() - b.startTime.getTime());
  }

  async findByStatus(status: string): Promise<Appointment[]> {
    return this.appointments
      .filter((a) => a.status === status)
      .sort((a, b) => a.startTime.getTime() - b.startTime.getTime());
  }

  async findConflictingAppointments(
    staffId: string,
    startTime: Date,
    endTime: Date,
    excludeId?: string,
  ): Promise<Appointment[]> {
    return this.appointments.filter((a) => {
      if (a.staffId !== staffId) return false;
      if (excludeId && a.id === excludeId) return false;

      return (
        (startTime < a.endTime && endTime > a.startTime) ||
        (a.startTime < endTime && a.endTime > startTime) ||
        (a.startTime >= startTime && a.endTime <= endTime)
      );
    });
  }

  async findAvailableSlots(
    salonId: string,
    staffId: string,
    date: Date,
    duration: number,
  ): Promise<{ start: Date; end: Date }[]> {
    // Simplified implementation for testing
    const startOfDay = new Date(date);
    startOfDay.setHours(9, 0, 0, 0);

    const endOfDay = new Date(date);
    endOfDay.setHours(17, 0, 0, 0);

    return [
      {
        start: startOfDay,
        end: new Date(startOfDay.getTime() + duration * 60 * 1000),
      },
    ];
  }
}
