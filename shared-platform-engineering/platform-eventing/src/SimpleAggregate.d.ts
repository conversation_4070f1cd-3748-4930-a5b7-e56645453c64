/**
 * 🏛️ Simple Aggregate
 *
 * Basic event-driven aggregate for command processing with NATS
 */
import { EventTypes } from './EventBuilder';
import type { OutboxManager } from './OutboxManager';
import type { Command, CommandResult, DomainEvent, EventMetadata, SimpleAggregate } from './types';
/**
 * 🏛️ Base Aggregate
 *
 * Simple foundation for event-driven aggregates that integrates with
 * the platform eventing infrastructure
 */
export declare abstract class BaseAggregate<TState = unknown> implements SimpleAggregate<TState> {
    readonly id: string;
    state: TState | null;
    protected version: number;
    protected outboxManager?: OutboxManager;
    constructor(id: string, initialState?: TState, outboxManager?: OutboxManager);
    /**
     * 🎯 Process a command and return events
     */
    abstract processCommand(command: Command): DomainEvent[];
    /**
     * 🎯 Process a command and return a structured result
     */
    processCommandWithResult(command: Command): CommandResult;
    /**
     * 🎯 Process command and store events in outbox atomically
     *
     * This method should be called within a database transaction
     * to ensure atomicity between business logic and event storage
     */
    processCommandWithOutbox(command: Command, transaction?: unknown): Promise<CommandResult>;
    /**
     * 🎪 Create a domain event using the platform event builder
     */
    protected createEvent<TData>(eventType: string, data: TData, options?: {
        correlationId?: string;
        causationId?: string;
        userId?: string;
        metadata?: EventMetadata;
    }): DomainEvent<TData>;
    /**
     * 🎪 Create a domain event with command context
     */
    protected createEventFromCommand<TData>(eventType: string, data: TData, command: Command): DomainEvent<TData>;
    /**
     * 🎪 Create event using EventTypes helper
     */
    protected createTypedEvent<TData>(action: keyof typeof EventTypes, data: TData, options?: {
        correlationId?: string;
        causationId?: string;
        userId?: string;
        metadata?: EventMetadata;
    }): DomainEvent<TData>;
    /**
     * 🏷️ Get aggregate type name
     */
    protected abstract getAggregateType(): string;
    /**
     * 🌍 Get source service name
     */
    protected abstract getSource(): string;
    /**
     * 📊 Get current version
     */
    getVersion(): number;
    /**
     * 🔄 Apply state changes (for derived classes)
     */
    protected applyStateChange(stateUpdater: (state: TState | null) => TState): void;
    /**
     * 🔄 Restore version (for factory use)
     */
    restoreVersion(version: number): void;
    /**
     * 📦 Set outbox manager for event storage
     */
    setOutboxManager(outboxManager: OutboxManager): void;
    /**
     * ✅ Validate command before processing
     */
    protected validateCommand(command: Command): void;
    /**
     * 🚫 Throw domain error
     */
    protected throwDomainError(message: string, code?: string): never;
}
/**
 * Extract the state type from an aggregate
 */
export type AggregateState<T> = T extends BaseAggregate<infer S> ? S : never;
/**
 * Constructor type for aggregates
 */
export type AggregateConstructor<T extends BaseAggregate = BaseAggregate> = new (id: string, initialState?: AggregateState<T>, outboxManager?: OutboxManager) => T;
/**
 * 🏭 Simple Aggregate Factory
 */
export declare const AggregateFactory: {
    /**
     * 🏗️ Create new aggregate instance
     */
    create<T extends BaseAggregate>(AggregateClass: AggregateConstructor<T>, id: string, initialState?: AggregateState<T>, outboxManager?: OutboxManager): T;
    /**
     * 🔄 Create aggregate with state restoration
     */
    restore<T extends BaseAggregate>(AggregateClass: AggregateConstructor<T>, id: string, state: AggregateState<T>, version?: number, outboxManager?: OutboxManager): T;
};
/**
 * 🏗️ Create a new aggregate instance
 */
export declare function createAggregate<T extends BaseAggregate>(AggregateClass: AggregateConstructor<T>, id: string, initialState?: AggregateState<T>, outboxManager?: OutboxManager): T;
//# sourceMappingURL=SimpleAggregate.d.ts.map