{"version": 3, "file": "EventBuilder.js", "sourceRoot": "", "sources": ["EventBuilder.ts"], "names": [], "mappings": "AAAA;;;;GAIG;AAIH;;;;GAIG;AACH,MAAM,OAAO,YAAY;IACf,KAAK,GAAgC;QAC3C,YAAY,EAAE,CAAC;QACf,QAAQ,EAAE,EAAE;QACZ,SAAS,EAAE,IAAI,IAAI,EAAE,CAAC,WAAW,EAAE;KACpC,CAAC;IAEF;;OAEG;IACH,EAAE,CAAC,OAAe;QAChB,IAAI,CAAC,KAAK,CAAC,OAAO,GAAG,OAAO,CAAC;QAC7B,OAAO,IAAI,CAAC;IACd,CAAC;IAED;;OAEG;IACH,IAAI,CAAC,SAAiB;QACpB,IAAI,CAAC,KAAK,CAAC,SAAS,GAAG,SAAS,CAAC;QACjC,OAAO,IAAI,CAAC;IACd,CAAC;IAED;;OAEG;IACH,SAAS,CAAC,WAAmB,EAAE,aAAqB;QAClD,IAAI,CAAC,KAAK,CAAC,WAAW,GAAG,WAAW,CAAC;QACrC,IAAI,CAAC,KAAK,CAAC,aAAa,GAAG,aAAa,CAAC;QACzC,OAAO,IAAI,CAAC;IACd,CAAC;IAED;;OAEG;IACH,IAAI,CAAC,IAAW;QACd,IAAI,CAAC,KAAK,CAAC,IAAI,GAAG,IAAI,CAAC;QACvB,OAAO,IAAI,CAAC;IACd,CAAC;IAED;;OAEG;IACH,MAAM,CAAC,MAAc;QACnB,IAAI,CAAC,KAAK,CAAC,MAAM,GAAG,MAAM,CAAC;QAC3B,OAAO,IAAI,CAAC;IACd,CAAC;IAED;;OAEG;IACH,SAAS,CAAC,SAAwB;QAChC,IAAI,CAAC,KAAK,CAAC,SAAS;YAClB,SAAS,YAAY,IAAI,CAAC,CAAC,CAAC,SAAS,CAAC,WAAW,EAAE,CAAC,CAAC,CAAC,SAAS,CAAC;QAClE,OAAO,IAAI,CAAC;IACd,CAAC;IAED;;OAEG;IACH,OAAO,CAAC,OAAe;QACrB,IAAI,CAAC,KAAK,CAAC,YAAY,GAAG,OAAO,CAAC;QAClC,OAAO,IAAI,CAAC;IACd,CAAC;IAED;;OAEG;IACH,aAAa,CAAC,aAAqB;QACjC,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,QAAQ;YAAE,IAAI,CAAC,KAAK,CAAC,QAAQ,GAAG,EAAE,CAAC;QACnD,IAAI,CAAC,KAAK,CAAC,QAAQ,CAAC,aAAa,GAAG,aAAa,CAAC;QAClD,OAAO,IAAI,CAAC;IACd,CAAC;IAED;;OAEG;IACH,WAAW,CAAC,WAAmB;QAC7B,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,QAAQ;YAAE,IAAI,CAAC,KAAK,CAAC,QAAQ,GAAG,EAAE,CAAC;QACnD,IAAI,CAAC,KAAK,CAAC,QAAQ,CAAC,WAAW,GAAG,WAAW,CAAC;QAC9C,OAAO,IAAI,CAAC;IACd,CAAC;IAED;;OAEG;IACH,MAAM,CAAC,MAAc;QACnB,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,QAAQ;YAAE,IAAI,CAAC,KAAK,CAAC,QAAQ,GAAG,EAAE,CAAC;QACnD,IAAI,CAAC,KAAK,CAAC,QAAQ,CAAC,MAAM,GAAG,MAAM,CAAC;QACpC,OAAO,IAAI,CAAC;IACd,CAAC;IAOD,QAAQ,CAAC,aAAqC,EAAE,KAAe;QAC7D,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,QAAQ;YAAE,IAAI,CAAC,KAAK,CAAC,QAAQ,GAAG,EAAE,CAAC;QAEnD,IAAI,OAAO,aAAa,KAAK,QAAQ,EAAE,CAAC;YACtC,IAAI,CAAC,KAAK,CAAC,QAAQ,CAAC,aAAa,CAAC,GAAG,KAAK,CAAC;QAC7C,CAAC;aAAM,CAAC;YACN,IAAI,CAAC,KAAK,CAAC,QAAQ,GAAG,EAAE,GAAG,IAAI,CAAC,KAAK,CAAC,QAAQ,EAAE,GAAG,aAAa,EAAE,CAAC;QACrE,CAAC;QACD,OAAO,IAAI,CAAC;IACd,CAAC;IAED;;OAEG;IACH,KAAK;QACH,oCAAoC;QACpC,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,OAAO,EAAE,CAAC;YACxB,IAAI,CAAC,KAAK,CAAC,OAAO,GAAG,MAAM,CAAC,UAAU,EAAE,CAAC;QAC3C,CAAC;QAED,2BAA2B;QAC3B,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,SAAS,EAAE,CAAC;YAC1B,MAAM,IAAI,KAAK,CAAC,2BAA2B,CAAC,CAAC;QAC/C,CAAC;QACD,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,WAAW,EAAE,CAAC;YAC5B,MAAM,IAAI,KAAK,CAAC,6BAA6B,CAAC,CAAC;QACjD,CAAC;QACD,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,aAAa,EAAE,CAAC;YAC9B,MAAM,IAAI,KAAK,CAAC,+BAA+B,CAAC,CAAC;QACnD,CAAC;QACD,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,MAAM,EAAE,CAAC;YACvB,MAAM,IAAI,KAAK,CAAC,uBAAuB,CAAC,CAAC;QAC3C,CAAC;QACD,IAAI,IAAI,CAAC,KAAK,CAAC,IAAI,KAAK,SAAS,EAAE,CAAC;YAClC,MAAM,IAAI,KAAK,CAAC,2BAA2B,CAAC,CAAC;QAC/C,CAAC;QAED,OAAO,IAAI,CAAC,KAA2B,CAAC;IAC1C,CAAC;CACF;AAED,+EAA+E;AAC/E,wBAAwB;AACxB,+EAA+E;AAE/E;;GAEG;AACH,MAAM,UAAU,WAAW;IACzB,OAAO,IAAI,YAAY,EAAS,CAAC;AACnC,CAAC;AAED;;GAEG;AACH,MAAM,UAAU,cAAc,CAC5B,aAAqB,EACrB,WAAmB;IAEnB,OAAO,IAAI,YAAY,EAAS,CAAC,SAAS,CAAC,WAAW,EAAE,aAAa,CAAC,CAAC;AACzE,CAAC;AAED;;GAEG;AACH,MAAM,UAAU,iBAAiB,CAAkB,OASlD;IACC,MAAM,OAAO,GAAG,WAAW,EAAS;SACjC,IAAI,CAAC,OAAO,CAAC,SAAS,CAAC;SACvB,SAAS,CAAC,OAAO,CAAC,WAAW,EAAE,OAAO,CAAC,aAAa,CAAC;SACrD,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC;SAClB,MAAM,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC;IAE1B,IAAI,OAAO,CAAC,aAAa,EAAE,CAAC;QAC1B,OAAO,CAAC,aAAa,CAAC,OAAO,CAAC,aAAa,CAAC,CAAC;IAC/C,CAAC;IAED,IAAI,OAAO,CAAC,MAAM,EAAE,CAAC;QACnB,OAAO,CAAC,MAAM,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC;IACjC,CAAC;IAED,IAAI,OAAO,CAAC,QAAQ,EAAE,CAAC;QACrB,OAAO,CAAC,QAAQ,CAAC,OAAO,CAAC,QAAQ,CAAC,CAAC;IACrC,CAAC;IAED,OAAO,OAAO,CAAC,KAAK,EAAE,CAAC;AACzB,CAAC;AAED,+EAA+E;AAC/E,qBAAqB;AACrB,+EAA+E;AAE/E;;GAEG;AACH,MAAM,UAAU,SAAS,CAAC,SAAiB,EAAE,MAAc;IACzD,OAAO,GAAG,SAAS,CAAC,WAAW,EAAE,IAAI,MAAM,CAAC,WAAW,EAAE,EAAE,CAAC;AAC9D,CAAC;AAED;;GAEG;AACH,MAAM,CAAC,MAAM,UAAU,GAAG;IACxB,gBAAgB;IAChB,SAAS,EAAE,CAAC,SAAiB,EAAE,EAAE,CAAC,SAAS,CAAC,SAAS,EAAE,WAAW,CAAC;IACnE,QAAQ,EAAE,CAAC,SAAiB,EAAE,EAAE,CAAC,SAAS,CAAC,SAAS,EAAE,UAAU,CAAC;IACjE,SAAS,EAAE,CAAC,SAAiB,EAAE,EAAE,CAAC,SAAS,CAAC,SAAS,EAAE,WAAW,CAAC;IACnE,SAAS,EAAE,CAAC,SAAiB,EAAE,EAAE,CAAC,SAAS,CAAC,SAAS,EAAE,WAAW,CAAC;IACnE,SAAS,EAAE,CAAC,SAAiB,EAAE,EAAE,CAAC,SAAS,CAAC,SAAS,EAAE,WAAW,CAAC;IACnE,kBAAkB;IAClB,OAAO,EAAE,CAAC,SAAiB,EAAE,EAAE,CAAC,SAAS,CAAC,SAAS,EAAE,SAAS,CAAC;IAC/D,WAAW,EAAE,CAAC,SAAiB,EAAE,EAAE,CAAC,SAAS,CAAC,SAAS,EAAE,aAAa,CAAC;IACvE,OAAO,EAAE,CAAC,SAAiB,EAAE,EAAE,CAAC,SAAS,CAAC,SAAS,EAAE,SAAS,CAAC;IAC/D,WAAW,EAAE,CAAC,SAAiB,EAAE,EAAE,CAAC,SAAS,CAAC,SAAS,EAAE,aAAa,CAAC;IAEvE,uBAAuB;IACvB,SAAS,EAAE,CAAC,SAAiB,EAAE,EAAE,CAAC,SAAS,CAAC,SAAS,EAAE,WAAW,CAAC;IAEnE,kBAAkB;IAClB,MAAM,EAAE,CAAC,SAAiB,EAAE,EAAE,CAAC,SAAS,CAAC,SAAS,EAAE,QAAQ,CAAC;IAC7D,UAAU,EAAE,CAAC,SAAiB,EAAE,EAAE,CAAC,SAAS,CAAC,SAAS,EAAE,aAAa,CAAC;IACtE,OAAO,EAAE,CAAC,SAAiB,EAAE,EAAE,CAAC,SAAS,CAAC,SAAS,EAAE,SAAS,CAAC;CACvD,CAAC;AAEX,+EAA+E;AAC/E,mBAAmB;AACnB,+EAA+E;AAE/E;;GAEG;AACH,MAAM,OAAO,cAAc;IACjB,KAAK,GAAa,EAAE,CAAC;IAE7B;;OAEG;IACH,SAAS,CAAC,IAAY;QACpB,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,WAAW,EAAE,CAAC,CAAC;QACpC,OAAO,IAAI,CAAC;IACd,CAAC;IAED;;OAEG;IACH,MAAM;QACJ,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;QAC1B,OAAO,IAAI,CAAC;IACd,CAAC;IAED;;OAEG;IACH,MAAM,CAAC,MAAc;QACnB,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,MAAM,CAAC,WAAW,EAAE,CAAC,CAAC;QACtC,OAAO,IAAI,CAAC;IACd,CAAC;IAED;;OAEG;IACH,EAAE,CAAC,EAAU;QACX,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;QACpB,OAAO,IAAI,CAAC;IACd,CAAC;IAED;;OAEG;IACH,QAAQ;QACN,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;QACrB,OAAO,IAAI,CAAC;IACd,CAAC;IAED;;OAEG;IACH,KAAK;QACH,OAAO,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;IAC9B,CAAC;CACF;AAED;;GAEG;AACH,MAAM,UAAU,OAAO;IACrB,OAAO,IAAI,cAAc,EAAE,CAAC;AAC9B,CAAC;AAED;;GAEG;AACH,MAAM,CAAC,MAAM,QAAQ,GAAG;IACtB,4BAA4B;IAC5B,eAAe,EAAE,CAAC,SAAiB,EAAE,EAAU,EAAE,EAAE,CACjD,OAAO,EAAE,CAAC,SAAS,CAAC,SAAS,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,MAAM,EAAE,CAAC,QAAQ,EAAE,CAAC,KAAK,EAAE;IACnE,8BAA8B;IAC9B,SAAS,EAAE,CAAC,SAAiB,EAAE,EAAE,CAC/B,OAAO,EAAE,CAAC,SAAS,CAAC,SAAS,CAAC,CAAC,MAAM,EAAE,CAAC,QAAQ,EAAE,CAAC,KAAK,EAAE;IAE5D,sBAAsB;IACtB,SAAS,EAAE,CAAC,SAAiB,EAAE,MAAc,EAAE,EAAE,CAC/C,OAAO,EAAE,CAAC,SAAS,CAAC,SAAS,CAAC,CAAC,MAAM,EAAE,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC,KAAK,EAAE;CACxD,CAAC"}