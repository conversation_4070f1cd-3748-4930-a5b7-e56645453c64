/**
 * 🌟 Beautiful Eventing Platform Types
 *
 * Elegant abstractions for event-driven architecture with NATS JetStream
 */
/**
 * Base event structure for all domain events
 */
export interface DomainEvent<TData = unknown> {
    /** Unique identifier for this event */
    eventId: string;
    /** Type of event (e.g., 'appointment.created', 'staff.updated') */
    eventType: string;
    /** ID of the aggregate that generated this event */
    aggregateId: string;
    /** Type of aggregate (e.g., 'appointment', 'staff', 'treatment') */
    aggregateType: string;
    /** Event payload data */
    data: TData;
    /** When the event occurred */
    timestamp: string;
    /** Version of the event schema */
    eventVersion: number;
    /** Source service that generated the event */
    source: string;
    /** Optional metadata for correlation, causation, etc. */
    metadata?: EventMetadata;
}
/**
 * Event metadata for tracing and correlation
 */
export interface EventMetadata {
    /** Correlation ID for request tracing */
    correlationId?: string;
    /** Causation ID linking to the event that caused this one */
    causationId?: string;
    /** User who triggered this event */
    userId?: string;
    /** Additional context-specific metadata */
    [key: string]: unknown;
}
/**
 * Configuration for JetStream streams
 */
export interface StreamConfig {
    /** Name of the stream */
    name: string;
    /** Subjects this stream will capture */
    subjects: string[];
    /** Maximum age of messages (in nanoseconds) */
    maxAge?: number;
    /** Maximum number of messages */
    maxMessages?: number;
    /** Storage type: 'file' or 'memory' */
    storage?: 'file' | 'memory';
    /** Retention policy: 'limits', 'interest', or 'workqueue' */
    retention?: 'limits' | 'interest' | 'workqueue';
    /** Number of replicas */
    replicas?: number;
    /** Description of the stream */
    description?: string;
}
/**
 * Configuration for event publishers
 */
export interface PublisherConfig {
    /** NATS server URL */
    natsUrl?: string;
    /** Service name for connection identification */
    serviceName: string;
    /** Stream configuration */
    stream?: StreamConfig;
    /** Connection options */
    connection?: {
        maxReconnectAttempts?: number;
        reconnectTimeWait?: number;
        timeout?: number;
    };
    /** Publishing options */
    publishing?: {
        /** Default headers to include with all events */
        defaultHeaders?: Record<string, string>;
        /** Whether to wait for acknowledgment */
        waitForAck?: boolean;
        /** Timeout for acknowledgments */
        ackTimeout?: number;
    };
}
/**
 * Configuration for event subscribers
 */
export interface SubscriberConfig {
    /** NATS server URL */
    natsUrl?: string;
    /** NATS server URLs */
    servers?: string[];
    /** Service name for connection identification */
    serviceName: string;
    /** Consumer configuration */
    consumer?: {
        /** Consumer name */
        name?: string;
        /** Delivery policy */
        deliverPolicy?: 'all' | 'last' | 'new' | 'by_start_sequence' | 'by_start_time';
        /** Acknowledgment policy */
        ackPolicy?: 'none' | 'all' | 'explicit';
        /** Maximum delivery attempts */
        maxDeliver?: number;
        /** Acknowledgment timeout */
        ackWait?: number;
    };
    /** Connection options */
    connection?: {
        maxReconnectAttempts?: number;
        reconnectTimeWait?: number;
        timeout?: number;
    };
}
/** Subscription options */
export interface SubscriptionOptions {
    /** Consumer name override */
    consumerName?: string;
    /** Acknowledgment policy override */
    ackPolicy?: 'none' | 'all' | 'explicit';
    /** Delivery policy override */
    deliverPolicy?: 'all' | 'last' | 'new' | 'by_start_sequence' | 'by_start_time';
}
/**
 * Event handler function
 */
export type EventHandler<TEvent extends DomainEvent = DomainEvent> = (event: TEvent) => Promise<void> | void;
/**
 * Error handler function
 */
export type ErrorHandler = (error: Error, event?: DomainEvent) => Promise<void> | void;
/**
 * Connection status
 */
export type ConnectionStatus = 'disconnected' | 'connecting' | 'connected' | 'reconnecting' | 'closed' | 'error';
/**
 * Health check result
 */
export interface HealthCheckResult {
    /** Whether the service is healthy */
    healthy: boolean;
    /** Connection status */
    status: ConnectionStatus;
    /** Additional details */
    details?: {
        /** Last successful operation timestamp */
        lastSuccess?: string;
        /** Error message if unhealthy */
        error?: string;
        /** Stream information */
        streams?: Array<{
            name: string;
            messages: number;
            bytes: number;
        }>;
        /** Active subscriptions */
        subscriptions?: string[];
    };
}
/**
 * Command interface for CQRS
 */
export interface Command<TData = unknown> {
    /** Type of command */
    commandType: string;
    /** Command payload */
    data: TData;
    /** Command metadata */
    metadata: {
        /** Correlation ID for tracing */
        correlationId: string;
        /** User who issued the command */
        userId?: string;
        /** When the command was issued */
        timestamp: Date;
        /** Additional metadata */
        [key: string]: unknown;
    };
}
/**
 * Command result
 */
export interface CommandResult {
    /** Whether command succeeded */
    success: boolean;
    /** Aggregate ID that was affected */
    aggregateId: string;
    /** New version after command */
    version?: number;
    /** Events that were generated */
    events?: string[];
    /** Error message if failed */
    error?: string;
}
/**
 * Simple aggregate interface for event-driven patterns
 */
export interface SimpleAggregate<TState = unknown> {
    /** Aggregate ID */
    id: string;
    /** Current state */
    state: TState | null;
    /** Process a command and return events */
    processCommand(command: Command): DomainEvent[];
}
//# sourceMappingURL=types.d.ts.map