/**
 * 🏛️ Simple Aggregate
 *
 * Basic event-driven aggregate for command processing with NATS
 */
import { createEvent, EventTypes } from './EventBuilder';
/**
 * 🏛️ Base Aggregate
 *
 * Simple foundation for event-driven aggregates that integrates with
 * the platform eventing infrastructure
 */
export class BaseAggregate {
    id;
    state = null;
    version = 0;
    outboxManager;
    constructor(id, initialState, outboxManager) {
        this.id = id;
        this.state = initialState || null;
        this.outboxManager = outboxManager;
    }
    /**
     * 🎯 Process a command and return a structured result
     */
    processCommandWithResult(command) {
        try {
            const events = this.processCommand(command);
            this.version += events.length;
            return {
                aggregateId: this.id,
                events: events.map((e) => e.eventId),
                success: true,
                version: this.version,
            };
        }
        catch (error) {
            return {
                aggregateId: this.id,
                error: error instanceof Error ? error.message : 'Unknown error',
                success: false,
                version: this.version,
            };
        }
    }
    /**
     * 🎯 Process command and store events in outbox atomically
     *
     * This method should be called within a database transaction
     * to ensure atomicity between business logic and event storage
     */
    async processCommandWithOutbox(command, transaction) {
        try {
            const events = this.processCommand(command);
            this.version += events.length;
            // Store events in outbox if manager is available
            if (this.outboxManager && events.length > 0) {
                await this.outboxManager.storeEvents(events, transaction);
            }
            return {
                aggregateId: this.id,
                events: events.map((e) => e.eventId),
                success: true,
                version: this.version,
            };
        }
        catch (error) {
            return {
                aggregateId: this.id,
                error: error instanceof Error ? error.message : 'Unknown error',
                success: false,
                version: this.version,
            };
        }
    }
    // ============================================================================
    // Event Creation Helpers
    // ============================================================================
    /**
     * 🎪 Create a domain event using the platform event builder
     */
    createEvent(eventType, data, options) {
        const builder = createEvent()
            .type(eventType)
            .aggregate(this.id, this.getAggregateType())
            .data(data)
            .source(this.getSource())
            .version(this.version + 1);
        // Add correlation and causation tracking
        if (options?.correlationId) {
            builder.correlationId(options.correlationId);
        }
        if (options?.causationId) {
            builder.causationId(options.causationId);
        }
        if (options?.userId) {
            builder.userId(options.userId);
        }
        if (options?.metadata) {
            builder.metadata(options.metadata);
        }
        return builder.build();
    }
    /**
     * 🎪 Create a domain event with command context
     */
    createEventFromCommand(eventType, data, command) {
        return this.createEvent(eventType, data, {
            correlationId: command.metadata.correlationId,
            metadata: {
                commandType: command.commandType,
                ...command.metadata,
            },
            userId: command.metadata.userId,
        });
    }
    /**
     * 🎪 Create event using EventTypes helper
     */
    createTypedEvent(action, data, options) {
        const eventType = EventTypes[action](this.getAggregateType());
        return this.createEvent(eventType, data, options);
    }
    // ============================================================================
    // State Management
    // ============================================================================
    /**
     * 📊 Get current version
     */
    getVersion() {
        return this.version;
    }
    /**
     * 🔄 Apply state changes (for derived classes)
     */
    applyStateChange(stateUpdater) {
        this.state = stateUpdater(this.state);
    }
    /**
     * 🔄 Restore version (for factory use)
     */
    restoreVersion(version) {
        this.version = version;
    }
    /**
     * 📦 Set outbox manager for event storage
     */
    setOutboxManager(outboxManager) {
        this.outboxManager = outboxManager;
    }
    // ============================================================================
    // Validation Helpers
    // ============================================================================
    /**
     * ✅ Validate command before processing
     */
    validateCommand(command) {
        if (!command.commandType) {
            throw new Error('Command type is required');
        }
        if (!command.metadata?.correlationId) {
            throw new Error('Correlation ID is required for command tracing');
        }
    }
    /**
     * 🚫 Throw domain error
     */
    throwDomainError(message, code) {
        const error = new Error(message);
        if (code) {
            error.code = code;
        }
        throw error;
    }
}
// ============================================================================
// Factory Functions
// ============================================================================
/**
 * 🏭 Simple Aggregate Factory
 */
export const AggregateFactory = {
    /**
     * 🏗️ Create new aggregate instance
     */
    create(AggregateClass, id, initialState, outboxManager) {
        return new AggregateClass(id, initialState, outboxManager);
    },
    /**
     * 🔄 Create aggregate with state restoration
     */
    restore(AggregateClass, id, state, version = 0, outboxManager) {
        const aggregate = new AggregateClass(id, state, outboxManager);
        // Use the public method to restore version
        aggregate.restoreVersion(version);
        return aggregate;
    },
};
/**
 * 🏗️ Create a new aggregate instance
 */
export function createAggregate(AggregateClass, id, initialState, outboxManager) {
    return AggregateFactory.create(AggregateClass, id, initialState, outboxManager);
}
//# sourceMappingURL=SimpleAggregate.js.map