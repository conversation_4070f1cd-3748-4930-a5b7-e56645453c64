import { StringCodec } from 'nats';
import { EventPublisher } from '../EventPublisher';
export class NatsPublisher extends EventPublisher {
  constructor(opts) {
    super({
      natsUrl: opts.natsUrl,
      serviceName: opts.serviceName,
      stream: {
        name: opts.streamName,
        subjects: opts.subjects,
      },
    });
    this.opts = opts;
  }
  async publish(event) {
    if (!this.isConnected()) {
      throw new Error('NATS connection not established');
    }
    const js = this.jetStream;
    if (!js) {
      throw new Error('JetStream not initialized');
    }
    const subject = `${this.opts.streamName}.${event.eventType}`;
    const data = StringCodec().encode(JSON.stringify(event));
    await js.publish(subject, data, {
      msgID: event.eventId,
    });
  }
  isConnected() {
    return super.isConnected();
  }
}
export function createNatsPublisher(options) {
  return new NatsPublisher(options);
}
//# sourceMappingURL=NatsPublisher.js.map
