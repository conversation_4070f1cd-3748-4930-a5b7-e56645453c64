import { EventPublisher } from '../EventPublisher';
import type { DomainEvent } from '../types';
export interface NatsPublisherOptions {
  natsUrl: string;
  streamName: string;
  serviceName: string;
  subjects: string[];
}
export declare class NatsPublisher extends EventPublisher {
  private opts;
  constructor(opts: NatsPublisherOptions);
  publish(event: DomainEvent): Promise<void>;
  isConnected(): boolean;
}
export declare function createNatsPublisher(
  options: NatsPublisherOptions,
): NatsPublisher;
//# sourceMappingURL=NatsPublisher.d.ts.map
