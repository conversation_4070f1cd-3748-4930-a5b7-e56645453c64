/**
 * 📡 EventSubscriber
 *
 * Elegant, type-safe event subscription with NATS JetStream
 */
import type { ConnectionStatus, DomainEvent, EventHandler, HealthCheckResult, SubscriberConfig, SubscriptionOptions } from './types';
/**
 * 📡 Beautiful Event Subscriber
 *
 * Elegant subscription to domain events with NATS JetStream
 */
export declare class EventSubscriber {
    private connection;
    private jetStream;
    private readonly jsonCodec;
    private readonly config;
    private status;
    private readonly subscriptions;
    private readonly handlers;
    private errorHandler?;
    constructor(config: SubscriberConfig);
    /**
     * 🔌 Connect to NATS
     */
    connect(): Promise<void>;
    /**
     * 📨 Subscribe to regular NATS subject
     */
    subscribe<T extends DomainEvent>(subject: string, handler: EventHandler<T>): Promise<void>;
    /**
     * 🌊 Subscribe to JetStream
     */
    subscribeToStream<T extends DomainEvent>(streamName: string, subject: string, handler: EventHandler<T>, options?: SubscriptionOptions): Promise<void>;
    /**
     * 🚨 Set error handler
     */
    onError(handler: (error: Error) => Promise<void>): void;
    /**
     * 🔄 Process regular NATS messages
     */
    private processMessages;
    /**
     * 🔌 Disconnect from NATS
     */
    disconnect(): Promise<void>;
    /**
     * 🏥 Health check
     */
    healthCheck(): Promise<HealthCheckResult>;
    /**
     * 📊 Get connection status
     */
    getStatus(): ConnectionStatus;
    /**
     * 🔍 Check if connected
     */
    isConnected(): boolean;
    /**
     * 📋 Get active subscriptions
     */
    getSubscriptions(): string[];
    /**
     * Maps local AckPolicy string literal to NATS AckPolicy enum.
     */
    private mapAckPolicy;
    /**
     * Maps local DeliverPolicy string literal to NATS DeliverPolicy type.
     */
    private mapDeliverPolicy;
}
/**
 * 🏭 Create a new EventSubscriber with configuration
 */
export declare function createSubscriber(config: {
    natsUrl?: string;
    serviceName: string;
    consumer?: {
        name: string;
        deliverPolicy?: 'new' | 'all' | 'last';
        ackPolicy?: 'explicit' | 'none' | 'all';
    };
}): EventSubscriber;
//# sourceMappingURL=EventSubscriber.d.ts.map