/**
 * 📦 Outbox Manager
 *
 * Transactional outbox pattern implementation for reliable event publishing
 * Provides atomic event storage and retrieval for Debezium CDC integration
 */
import { globalEventValidator } from './EventValidator';
// ============================================================================
// Outbox Manager Implementation
// ============================================================================
/**
 * 📦 Outbox Manager
 *
 * Manages transactional outbox operations for reliable event publishing
 */
export class OutboxManager {
    storage;
    config;
    constructor(storage, config) {
        this.storage = storage;
        this.config = {
            batchSize: 100,
            enableSchemaValidation: false,
            maxRetries: 3,
            retentionHours: 48,
            ...config,
            eventValidator: config.eventValidator || globalEventValidator,
        };
    }
    // ============================================================================
    // Event Storage
    // ============================================================================
    /**
     * 💾 Store events atomically in outbox
     *
     * This method should be called within the same database transaction
     * as the business logic to ensure atomicity
     */
    async storeEvents(events, transaction) {
        if (events.length === 0)
            return;
        // Validate events if schema validation is enabled
        if (this.config.enableSchemaValidation && this.config.eventValidator) {
            for (const event of events) {
                this.config.eventValidator.validateForOutbox(event);
            }
        }
        await this.storage.storeEvents(events, transaction);
    }
    /**
     * 💾 Store single event in outbox
     */
    async storeEvent(event, transaction) {
        await this.storeEvents([event], transaction);
    }
    // ============================================================================
    // Event Processing
    // ============================================================================
    /**
     * 📤 Get unprocessed events for publishing
     */
    async getUnprocessedEvents(limit) {
        const batchSize = limit || this.config.batchSize;
        return this.storage.getUnprocessedEvents(batchSize);
    }
    /**
     * ✅ Mark events as successfully processed
     */
    async markEventsProcessed(eventIds) {
        if (eventIds.length === 0)
            return;
        await this.storage.markEventsProcessed(eventIds);
    }
    /**
     * ❌ Mark event as failed with error message
     */
    async markEventFailed(eventId, errorMessage) {
        await this.storage.markEventFailed(eventId, errorMessage);
    }
    /**
     * 🔄 Increment retry count for failed events
     */
    async incrementRetryCount(eventId) {
        await this.storage.incrementRetryCount(eventId);
    }
    // ============================================================================
    // Maintenance Operations
    // ============================================================================
    /**
     * 🧹 Clean up processed events older than retention period
     */
    async cleanupProcessedEvents() {
        const cutoffDate = new Date();
        cutoffDate.setHours(cutoffDate.getHours() - this.config.retentionHours);
        return this.storage.cleanupProcessedEvents(cutoffDate);
    }
    /**
     * 📊 Get outbox statistics for monitoring
     */
    async getOutboxStats() {
        return this.storage.getOutboxStats();
    }
    /**
     * 🔍 Get configuration for debugging
     */
    getConfig() {
        return this.config;
    }
}
// ============================================================================
// Utility Functions
// ============================================================================
/**
 * 🔄 Convert DomainEvent to OutboxEntry
 */
export function domainEventToOutboxEntry(event, source) {
    return {
        aggregateId: event.aggregateId,
        aggregateType: event.aggregateType,
        eventData: event.data,
        eventId: event.eventId,
        eventMetadata: event.metadata,
        eventType: event.eventType,
        eventVersion: event.eventVersion,
        source: source,
        timestamp: new Date(event.timestamp),
    };
}
/**
 * 🔄 Convert OutboxEntry to DomainEvent
 */
export function outboxEntryToDomainEvent(entry) {
    return {
        aggregateId: entry.aggregateId,
        aggregateType: entry.aggregateType,
        data: entry.eventData,
        eventId: entry.eventId,
        eventType: entry.eventType,
        eventVersion: entry.eventVersion,
        metadata: entry.eventMetadata,
        source: entry.source,
        timestamp: entry.timestamp.toISOString(),
    };
}
//# sourceMappingURL=OutboxManager.js.map