/**
 * 🌟 Beautiful Eventing Platform
 *
 * Elegant event-driven architecture with NATS JetStream for Beauty CRM
 *
 * @example
 * ```typescript
 * import { NatsPublisher, createNatsPublisher } from '@beauty-crm/platform-eventing/nats';
 *
 * // Create a NATS publisher
 * const publisher = createNatsPublisher({
 *   natsUrl: 'nats://localhost:4222',
 *   streamName: 'APPOINTMENT_EVENTS',
 *   subjects: ['appointment.events.*'],
 *   serviceName: 'appointment-service'
 * });
 *
 * await publisher.connect();
 *
 * // Publish an event
 * await publisher.publish({
 *   eventType: 'appointment.created',
 *   aggregateId: 'appointment-123',
 *   aggregateType: 'appointment',
 *   data: { customerName: 'Alice', treatmentName: 'Facial' },
 *   metadata: {
 *     correlationId: 'req-456',
 *     source: 'appointment-service',
 *     timestamp: new Date().toISOString(),
 *     userId: 'user-123'
 *   }
 * });
 * ```
 */
export { createPublisher, EventPublisher } from './EventPublisher';
export { createSubscriber, EventSubscriber } from './EventSubscriber';
export { createPrismaOutboxStorage } from './PrismaOutboxStorage';
export * from './nats';
export * from './outbox';
export type { AggregateConstructor, AggregateState, } from './SimpleAggregate';
export { AggregateFactory, BaseAggregate, createAggregate, } from './SimpleAggregate';
export type { OutboxConfig, OutboxEntry, OutboxStats, OutboxStorage, } from './OutboxManager';
export { domainEventToOutboxEntry, OutboxManager, outboxEntryToDomainEvent, } from './OutboxManager';
export type { CompatibilityMode, EventSchema, SchemaCompatibilityResult, SchemaValidationResult, } from './EventSchemaRegistry';
export { EventSchemaRegistry, globalEventSchemaRegistry, } from './EventSchemaRegistry';
export type { BatchValidationResult, EventValidationResult, ValidationConfig, } from './EventValidator';
export { EventValidator, globalEventValidator, } from './EventValidator';
export type { AppointmentCreatedEventDataV1, AppointmentCreatedEventDataV2, } from './schemas/AppointmentEventSchemas';
export { appointmentCreatedSchemas, registerAppointmentSchemas, } from './schemas/AppointmentEventSchemas';
export { createDomainEvent, createEvent, createEventFor, EventBuilder, EventTypes, eventType, SubjectBuilder, Subjects, subject, } from './EventBuilder';
export type { Command, CommandResult, ConnectionStatus, DomainEvent, ErrorHandler, EventHandler, EventMetadata, HealthCheckResult, PublisherConfig, SimpleAggregate, StreamConfig, SubscriberConfig, } from './types';
/**
 * 📋 Pre-configured streams for common use cases
 */
export declare const StreamConfigs: {
    /**
     * 🌍 All events stream (for monitoring/auditing)
     */
    readonly all: {
        readonly description: "All domain events across the platform";
        readonly maxAge: number;
        readonly maxMessages: 100000;
        readonly name: "ALL_EVENTS";
        readonly subjects: readonly ["*.events.*"];
    };
    /**
     * 📅 Appointment events stream
     */
    readonly appointments: {
        readonly description: "All appointment lifecycle events";
        readonly name: "APPOINTMENT_EVENTS";
        readonly subjects: readonly ["appointment.events.*"];
    };
    /**
     * � Client events stream
     */
    readonly clients: {
        readonly description: "All client management events";
        readonly name: "CLIENT_EVENTS";
        readonly subjects: readonly ["client.events.*"];
    };
    /**
     * 🏢 Salon events stream
     */
    readonly salons: {
        readonly description: "All salon management events";
        readonly name: "SALON_EVENTS";
        readonly subjects: readonly ["salon.events.*"];
    };
    /**
     * � Staff events stream
     */
    readonly staff: {
        readonly description: "All staff management events";
        readonly name: "STAFF_EVENTS";
        readonly subjects: readonly ["staff.events.*"];
    };
    /**
     * 🔄 Sync events stream
     */
    readonly sync: {
        readonly description: "Cross-service synchronization events";
        readonly name: "SYNC_EVENTS";
        readonly subjects: readonly ["*.sync.*", "*.synced", "*.sync_failed"];
    };
    /**
     * 💆 Treatment events stream
     */
    readonly treatments: {
        readonly description: "All treatment management events";
        readonly name: "TREATMENT_EVENTS";
        readonly subjects: readonly ["treatment.events.*"];
    };
};
/**
 * 📤 Pre-configured publishers for services
 */
export declare const PublisherConfigs: {
    readonly appointment: (serviceName?: string) => {
        serviceName: string;
        stream: {
            readonly description: "All appointment lifecycle events";
            readonly name: "APPOINTMENT_EVENTS";
            readonly subjects: readonly ["appointment.events.*"];
        };
    };
    readonly client: (serviceName?: string) => {
        serviceName: string;
        stream: {
            readonly description: "All client management events";
            readonly name: "CLIENT_EVENTS";
            readonly subjects: readonly ["client.events.*"];
        };
    };
    readonly salon: (serviceName?: string) => {
        serviceName: string;
        stream: {
            readonly description: "All salon management events";
            readonly name: "SALON_EVENTS";
            readonly subjects: readonly ["salon.events.*"];
        };
    };
    readonly staff: (serviceName?: string) => {
        serviceName: string;
        stream: {
            readonly description: "All staff management events";
            readonly name: "STAFF_EVENTS";
            readonly subjects: readonly ["staff.events.*"];
        };
    };
    readonly treatment: (serviceName?: string) => {
        serviceName: string;
        stream: {
            readonly description: "All treatment management events";
            readonly name: "TREATMENT_EVENTS";
            readonly subjects: readonly ["treatment.events.*"];
        };
    };
};
/**
 * 📡 Pre-configured subscribers for services
 */
export declare const SubscriberConfigs: {
    readonly appointment: (serviceName?: string) => {
        consumer: {
            ackPolicy: "explicit";
            deliverPolicy: "new";
            name: string;
        };
        serviceName: string;
    };
    readonly client: (serviceName?: string) => {
        consumer: {
            ackPolicy: "explicit";
            deliverPolicy: "new";
            name: string;
        };
        serviceName: string;
    };
    readonly salon: (serviceName?: string) => {
        consumer: {
            ackPolicy: "explicit";
            deliverPolicy: "new";
            name: string;
        };
        serviceName: string;
    };
    readonly staff: (serviceName?: string) => {
        consumer: {
            ackPolicy: "explicit";
            deliverPolicy: "new";
            name: string;
        };
        serviceName: string;
    };
    readonly treatment: (serviceName?: string) => {
        consumer: {
            ackPolicy: "explicit";
            deliverPolicy: "new";
            name: string;
        };
        serviceName: string;
    };
};
export declare const version = "1.0.0";
export declare const name = "@beauty-crm/platform-eventing";
/**
 * 🎉 Welcome message
 */
export declare function welcome(): void;
//# sourceMappingURL=index.d.ts.map