import type { AppointmentEvent } from '@beauty-crm/product-appointment-types';
interface DatabaseClient {
  $transaction<T>(fn: (tx: DatabaseClient) => Promise<T>): Promise<T>;
  $queryRawUnsafe<T>(query: string, ...values: unknown[]): Promise<T>;
  $executeRawUnsafe(query: string, ...values: unknown[]): Promise<number>;
}
/**
 * Interface for transactional outbox operations
 */
export interface ITransactionalOutbox {
  /**
   * Adds an event to the outbox within a transaction
   */
  add(event: AppointmentEvent, tx: DatabaseClient): Promise<void>;
  /**
   * Processes pending events from the outbox
   */
  processPendingEvents(
    processFn: (event: AppointmentEvent) => Promise<void>,
    batchSize?: number,
    maxRetries?: number,
  ): Promise<{
    processed: number;
    failed: number;
  }>;
}
/**
 * Default implementation of the transactional outbox using Prisma
 */
export declare class PrismaTransactionalOutbox implements ITransactionalOutbox {
  private readonly prisma;
  private readonly outboxTable;
  constructor(prisma: DatabaseClient, outboxTable?: string);
  add(event: AppointmentEvent, tx: DatabaseClient): Promise<void>;
  processPendingEvents(
    processFn: (event: AppointmentEvent) => Promise<void>,
    batchSize?: number,
    maxRetries?: number,
  ): Promise<{
    processed: number;
    failed: number;
  }>;
}
//# sourceMappingURL=TransactionalOutbox.d.ts.map
