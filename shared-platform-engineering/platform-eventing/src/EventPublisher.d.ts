/**
 * 🌟 Beautiful Event Publisher
 *
 * Elegant event publishing with NATS JetStream for Beauty CRM
 */
import { type JetStreamClient, type JetStreamManager, type NatsConnection } from 'nats';
import type { ConnectionStatus, DomainEvent, HealthCheckResult, PublisherConfig } from './types';
/**
 * 🌟 Beautiful Event Publisher
 *
 * Elegant event publishing with NATS JetStream
 */
export declare class EventPublisher {
    private config;
    protected connection: NatsConnection | null;
    protected jetStream: JetStreamClient | null;
    protected jetStreamManager: JetStreamManager | null;
    protected status: ConnectionStatus;
    private codec;
    constructor(config: PublisherConfig);
    /**
     * 🔗 Connect to NATS JetStream
     */
    connect(): Promise<void>;
    /**
     * 🔌 Disconnect from NATS
     */
    disconnect(): Promise<void>;
    /**
     * 📤 Publish a domain event
     */
    publish(event: DomainEvent): Promise<void>;
    /**
     * 🏗️ Ensure stream exists
     */
    private ensureStream;
    /**
     * 🏷️ Build subject for event
     */
    private buildSubject;
    /**
     * 🏥 Health check
     */
    healthCheck(): Promise<HealthCheckResult>;
    /**
     * 📊 Get current connection status
     */
    getStatus(): ConnectionStatus;
    /**
     * 🔍 Check if connected
     */
    isConnected(): boolean;
}
/**
 * 🏭 Create a new EventPublisher with configuration
 */
export declare function createPublisher(config: {
    natsUrl?: string;
    serviceName: string;
    stream?: {
        name: string;
        subjects: string[];
        retention?: 'limits' | 'interest' | 'workqueue';
        storage?: 'file' | 'memory';
        maxAge?: number;
        maxMessages?: number;
        replicas?: number;
    };
}): EventPublisher;
//# sourceMappingURL=EventPublisher.d.ts.map