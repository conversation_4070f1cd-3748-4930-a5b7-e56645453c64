/**
 * 🗄️ Prisma Outbox Storage
 *
 * Prisma implementation of OutboxStorage for transactional outbox pattern
 */
import type { OutboxEntry, OutboxStats, OutboxStorage } from './OutboxManager';
import type { DomainEvent } from './types';
/**
 * Minimal interface for the Prisma outbox model methods
 * Using `any` for complex Prisma generated types for simplicity
 */
interface OutboxModelMethods {
    create: (args: any) => Promise<OutboxEntry>;
    createMany: (args: any) => Promise<{
        count: number;
    }>;
    findMany: (args?: any) => Promise<any[]>;
    updateMany: (args: any) => Promise<{
        count: number;
    }>;
    update: (args: any) => Promise<OutboxEntry>;
    deleteMany: (args: any) => Promise<{
        count: number;
    }>;
    count: (args?: any) => Promise<number>;
    aggregate: (args: any) => Promise<any>;
}
/**
 * Minimal generic Prisma client interface for outbox operations
 * T must have a 'transactionalOutbox' property of type OutboxModelMethods
 */
export interface GenericPrismaClient<T extends {
    transactionalOutbox: OutboxModelMethods;
}> {
    transactionalOutbox: T['transactionalOutbox'];
    $transaction?: <R>(fn: (prisma: T) => Promise<R>) => Promise<R>;
}
/**
 * 🗄️ Prisma Outbox Storage
 *
 * Implements OutboxStorage using Prisma ORM for database operations
 */
export declare class PrismaOutboxStorage<TClient extends GenericPrismaClient<{
    transactionalOutbox: OutboxModelMethods;
}>> implements OutboxStorage {
    private readonly outboxModel;
    private readonly prisma;
    private readonly source;
    constructor(prisma: TClient, source: string);
    /**
     * 💾 Store events atomically in the outbox table
     */
    storeEvents(events: DomainEvent[], transaction?: TClient): Promise<void>;
    /**
     * 📤 Retrieve unprocessed events for publishing
     */
    getUnprocessedEvents(limit?: number): Promise<OutboxEntry[]>;
    /**
     * ✅ Mark events as processed
     */
    markEventsProcessed(eventIds: string[]): Promise<void>;
    /**
     * ❌ Mark event as failed with error message
     */
    markEventFailed(eventId: string, errorMessage: string): Promise<void>;
    /**
     * 🔄 Increment retry count for failed events
     */
    incrementRetryCount(eventId: string): Promise<void>;
    /**
     * 🧹 Clean up processed events older than specified date
     */
    cleanupProcessedEvents(olderThan: Date): Promise<number>;
    /**
     * 📊 Get outbox statistics for monitoring
     */
    getOutboxStats(): Promise<OutboxStats>;
    /**
     * 🔄 Map database entry to OutboxEntry interface
     */
    private mapToOutboxEntry;
}
/**
 * 🏭 Create Prisma Outbox Storage instance
 */
export declare function createPrismaOutboxStorage<TClient extends GenericPrismaClient<{
    transactionalOutbox: OutboxModelMethods;
}>>(prisma: TClient, source: string): PrismaOutboxStorage<TClient>;
export {};
//# sourceMappingURL=PrismaOutboxStorage.d.ts.map