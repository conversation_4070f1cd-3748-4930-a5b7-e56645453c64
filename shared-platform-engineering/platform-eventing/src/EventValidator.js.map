{"version": 3, "file": "EventValidator.js", "sourceRoot": "", "sources": ["EventValidator.ts"], "names": [], "mappings": "AAAA;;;;;GAKG;AAEH,OAAO,EAEL,yBAAyB,GAC1B,MAAM,uBAAuB,CAAC;AAc/B,MAAM,CAAC,MAAM,yBAAyB,GAAqB;IACzD,kBAAkB,EAAE,IAAI;IACxB,mBAAmB,EAAE,IAAI;IACzB,UAAU,EAAE,KAAK;CAClB,CAAC;AA+BF,+EAA+E;AAC/E,kBAAkB;AAClB,+EAA+E;AAE/E,MAAM,OAAO,cAAc;IACjB,cAAc,CAAsB;IACpC,MAAM,CAAmB;IAEjC,YACE,iBAAsC,yBAAyB,EAC/D,SAA2B,yBAAyB;QAEpD,IAAI,CAAC,cAAc,GAAG,cAAc,CAAC;QACrC,IAAI,CAAC,MAAM,GAAG,EAAE,GAAG,yBAAyB,EAAE,GAAG,MAAM,EAAE,CAAC;IAC5D,CAAC;IAED;;OAEG;IACH,aAAa,CAAC,KAAkB;QAC9B,MAAM,SAAS,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC;QAE7B,MAAM,MAAM,GAA0B;YACpC,MAAM,EAAE,EAAE;YACV,SAAS,EAAE,KAAK,CAAC,SAAS;YAC1B,QAAQ,EAAE;gBACR,WAAW,EAAE,KAAK;gBAClB,SAAS,EAAE,IAAI,IAAI,EAAE,CAAC,WAAW,EAAE;gBACnC,cAAc,EAAE,CAAC;aAClB;YACD,KAAK,EAAE,KAAK;YACZ,OAAO,EAAE,KAAK,CAAC,YAAY,IAAI,CAAC;YAChC,QAAQ,EAAE,EAAE;SACb,CAAC;QAEF,IAAI,CAAC;YACH,yBAAyB;YACzB,MAAM,SAAS,GAAG,IAAI,CAAC,cAAc,CAAC,YAAY,CAAC,KAAK,CAAC,SAAS,CAAC,CAAC;YACpE,MAAM,CAAC,QAAQ,CAAC,WAAW,GAAG,SAAS,CAAC;YAExC,IAAI,CAAC,SAAS,EAAE,CAAC;gBACf,IAAI,IAAI,CAAC,MAAM,CAAC,kBAAkB,EAAE,CAAC;oBACnC,MAAM,CAAC,KAAK,GAAG,IAAI,CAAC;oBACpB,MAAM,CAAC,QAAQ,CAAC,IAAI,CAClB,mCAAmC,KAAK,CAAC,SAAS,EAAE,CACrD,CAAC;gBACJ,CAAC;qBAAM,CAAC;oBACN,MAAM,CAAC,MAAM,CAAC,IAAI,CAChB,mCAAmC,KAAK,CAAC,SAAS,EAAE,CACrD,CAAC;gBACJ,CAAC;YACH,CAAC;iBAAM,CAAC;gBACN,0BAA0B;gBAC1B,MAAM,YAAY,GAAG,IAAI,CAAC,cAAc,CAAC,aAAa,CACpD,KAAK,CAAC,SAAS,EACf,KAAK,CAAC,IAAI,EACV,KAAK,CAAC,YAAY,IAAI,IAAI,CAAC,MAAM,CAAC,cAAc,CACjD,CAAC;gBAEF,MAAM,CAAC,KAAK,GAAG,YAAY,CAAC,KAAK,CAAC;gBAClC,MAAM,CAAC,OAAO,GAAG,YAAY,CAAC,OAAO,CAAC;gBAEtC,IAAI,YAAY,CAAC,MAAM,EAAE,CAAC;oBACxB,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC,GAAG,YAAY,CAAC,MAAM,CAAC,CAAC;gBAC7C,CAAC;gBAED,IAAI,YAAY,CAAC,QAAQ,EAAE,CAAC;oBAC1B,MAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,GAAG,YAAY,CAAC,QAAQ,CAAC,CAAC;gBACjD,CAAC;gBAED,oBAAoB;gBACpB,IAAI,IAAI,CAAC,MAAM,CAAC,UAAU,IAAI,MAAM,CAAC,QAAQ,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;oBACzD,MAAM,CAAC,KAAK,GAAG,KAAK,CAAC;oBACrB,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC,yCAAyC,CAAC,CAAC;gBAChE,CAAC;YACH,CAAC;YAED,2BAA2B;YAC3B,IAAI,CAAC,sBAAsB,CAAC,KAAK,EAAE,MAAM,CAAC,CAAC;QAC7C,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,MAAM,CAAC,KAAK,GAAG,KAAK,CAAC;YACrB,MAAM,CAAC,MAAM,CAAC,IAAI,CAChB,qBAAqB,KAAK,YAAY,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,eAAe,EAAE,CAChF,CAAC;QACJ,CAAC;QAED,MAAM,CAAC,QAAQ,CAAC,cAAc,GAAG,IAAI,CAAC,GAAG,EAAE,GAAG,SAAS,CAAC;QAExD,sCAAsC;QACtC,IAAI,IAAI,CAAC,MAAM,CAAC,mBAAmB,IAAI,CAAC,MAAM,CAAC,KAAK,EAAE,CAAC;YACrD,OAAO,CAAC,KAAK,CAAC,0BAA0B,EAAE;gBACxC,MAAM,EAAE,MAAM,CAAC,MAAM;gBACrB,OAAO,EAAE,KAAK,CAAC,OAAO;gBACtB,SAAS,EAAE,KAAK,CAAC,SAAS;gBAC1B,QAAQ,EAAE,MAAM,CAAC,QAAQ;aAC1B,CAAC,CAAC;QACL,CAAC;QAED,OAAO,MAAM,CAAC;IAChB,CAAC;IAED;;OAEG;IACH,cAAc,CAAC,MAAqB;QAClC,MAAM,SAAS,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC;QAC7B,MAAM,OAAO,GAA4B,EAAE,CAAC;QAC5C,MAAM,YAAY,GAA2B,EAAE,CAAC;QAChD,MAAM,cAAc,GAA2B,EAAE,CAAC;QAElD,KAAK,MAAM,KAAK,IAAI,MAAM,EAAE,CAAC;YAC3B,MAAM,MAAM,GAAG,IAAI,CAAC,aAAa,CAAC,KAAK,CAAC,CAAC;YACzC,OAAO,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;YAErB,uBAAuB;YACvB,KAAK,MAAM,KAAK,IAAI,MAAM,CAAC,MAAM,EAAE,CAAC;gBAClC,MAAM,SAAS,GAAG,IAAI,CAAC,eAAe,CAAC,KAAK,CAAC,CAAC;gBAC9C,YAAY,CAAC,SAAS,CAAC,GAAG,CAAC,YAAY,CAAC,SAAS,CAAC,IAAI,CAAC,CAAC,GAAG,CAAC,CAAC;YAC/D,CAAC;YAED,yBAAyB;YACzB,KAAK,MAAM,OAAO,IAAI,MAAM,CAAC,QAAQ,EAAE,CAAC;gBACtC,MAAM,WAAW,GAAG,IAAI,CAAC,iBAAiB,CAAC,OAAO,CAAC,CAAC;gBACpD,cAAc,CAAC,WAAW,CAAC,GAAG,CAAC,cAAc,CAAC,WAAW,CAAC,IAAI,CAAC,CAAC,GAAG,CAAC,CAAC;YACvE,CAAC;QACH,CAAC;QAED,MAAM,WAAW,GAAG,OAAO,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,MAAM,CAAC;QAC1D,MAAM,aAAa,GAAG,OAAO,CAAC,MAAM,GAAG,WAAW,CAAC;QAEnD,OAAO;YACL,aAAa;YACb,OAAO;YACP,OAAO,EAAE;gBACP,YAAY;gBACZ,cAAc,EAAE,IAAI,CAAC,GAAG,EAAE,GAAG,SAAS;gBACtC,cAAc;aACf;YACD,WAAW,EAAE,MAAM,CAAC,MAAM;YAC1B,WAAW;SACZ,CAAC;IACJ,CAAC;IAED;;OAEG;IACH,iBAAiB,CAAC,KAAkB;QAClC,MAAM,MAAM,GAAG,IAAI,CAAC,aAAa,CAAC,KAAK,CAAC,CAAC;QAEzC,IAAI,CAAC,MAAM,CAAC,KAAK,EAAE,CAAC;YAClB,MAAM,IAAI,KAAK,CACb,+CAA+C,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE,CAC1E,CAAC;QACJ,CAAC;QAED,IAAI,IAAI,CAAC,MAAM,CAAC,UAAU,IAAI,MAAM,CAAC,QAAQ,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;YACzD,MAAM,IAAI,KAAK,CACb,6CAA6C,MAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE,CAC1E,CAAC;QACJ,CAAC;IACH,CAAC;IAED;;OAEG;IACH,kBAAkB,CAAC,OAAgC;QAMjD,MAAM,WAAW,GAAG,OAAO,CAAC,MAAM,CAAC;QACnC,MAAM,WAAW,GAAG,OAAO,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,MAAM,CAAC;QAC1D,MAAM,WAAW,GAAG,WAAW,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,WAAW,GAAG,WAAW,CAAC,GAAG,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC;QAE5E,MAAM,mBAAmB,GAAG,OAAO,CAAC,MAAM,CACxC,CAAC,GAAG,EAAE,CAAC,EAAE,EAAE,CAAC,GAAG,GAAG,CAAC,CAAC,QAAQ,CAAC,cAAc,EAC3C,CAAC,CACF,CAAC;QACF,MAAM,qBAAqB,GACzB,WAAW,GAAG,CAAC,CAAC,CAAC,CAAC,mBAAmB,GAAG,WAAW,CAAC,CAAC,CAAC,CAAC,CAAC;QAE1D,eAAe;QACf,MAAM,WAAW,GAA2B,EAAE,CAAC;QAC/C,KAAK,MAAM,MAAM,IAAI,OAAO,EAAE,CAAC;YAC7B,KAAK,MAAM,KAAK,IAAI,MAAM,CAAC,MAAM,EAAE,CAAC;gBAClC,WAAW,CAAC,KAAK,CAAC,GAAG,CAAC,WAAW,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC,GAAG,CAAC,CAAC;YACrD,CAAC;QACH,CAAC;QAED,MAAM,gBAAgB,GAAG,MAAM,CAAC,OAAO,CAAC,WAAW,CAAC;aACjD,GAAG,CAAC,CAAC,CAAC,KAAK,EAAE,KAAK,CAAC,EAAE,EAAE,CAAC,CAAC,EAAE,KAAK,EAAE,KAAK,EAAE,CAAC,CAAC;aAC3C,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,KAAK,GAAG,CAAC,CAAC,KAAK,CAAC;aACjC,KAAK,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC;QAEhB,uBAAuB;QACvB,MAAM,kBAAkB,GAGpB,EAAE,CAAC;QACP,KAAK,MAAM,MAAM,IAAI,OAAO,EAAE,CAAC;YAC7B,IAAI,CAAC,kBAAkB,CAAC,MAAM,CAAC,SAAS,CAAC,EAAE,CAAC;gBAC1C,kBAAkB,CAAC,MAAM,CAAC,SAAS,CAAC,GAAG,EAAE,OAAO,EAAE,CAAC,EAAE,KAAK,EAAE,CAAC,EAAE,CAAC;YAClE,CAAC;YAED,IAAI,MAAM,CAAC,KAAK,EAAE,CAAC;gBACjB,kBAAkB,CAAC,MAAM,CAAC,SAAS,CAAC,CAAC,KAAK,EAAE,CAAC;YAC/C,CAAC;iBAAM,CAAC;gBACN,kBAAkB,CAAC,MAAM,CAAC,SAAS,CAAC,CAAC,OAAO,EAAE,CAAC;YACjD,CAAC;QACH,CAAC;QAED,OAAO;YACL,qBAAqB;YACrB,kBAAkB;YAClB,gBAAgB;YAChB,WAAW;SACZ,CAAC;IACJ,CAAC;IAED,+EAA+E;IAC/E,kBAAkB;IAClB,+EAA+E;IAEvE,sBAAsB,CAC5B,KAAkB,EAClB,MAA6B;QAE7B,uCAAuC;QACvC,IAAI,CAAC,KAAK,CAAC,OAAO,EAAE,CAAC;YACnB,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC,iCAAiC,CAAC,CAAC;QACxD,CAAC;QAED,IAAI,CAAC,KAAK,CAAC,SAAS,EAAE,CAAC;YACrB,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC,mCAAmC,CAAC,CAAC;QAC1D,CAAC;QAED,IAAI,CAAC,KAAK,CAAC,WAAW,EAAE,CAAC;YACvB,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC,qCAAqC,CAAC,CAAC;QAC5D,CAAC;QAED,IAAI,CAAC,KAAK,CAAC,aAAa,EAAE,CAAC;YACzB,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC,uCAAuC,CAAC,CAAC;QAC9D,CAAC;QAED,IAAI,CAAC,KAAK,CAAC,SAAS,EAAE,CAAC;YACrB,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC,mCAAmC,CAAC,CAAC;QAC1D,CAAC;QAED,IAAI,KAAK,CAAC,YAAY,KAAK,SAAS,IAAI,KAAK,CAAC,YAAY,GAAG,CAAC,EAAE,CAAC;YAC/D,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC,2BAA2B,CAAC,CAAC;QAClD,CAAC;QAED,4BAA4B;QAC5B,IAAI,KAAK,CAAC,SAAS,EAAE,CAAC;YACpB,IAAI,CAAC;gBACH,MAAM,IAAI,GAAG,IAAI,IAAI,CAAC,KAAK,CAAC,SAAS,CAAC,CAAC;gBACvC,IAAI,MAAM,CAAC,KAAK,CAAC,IAAI,CAAC,OAAO,EAAE,CAAC,EAAE,CAAC;oBACjC,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC,0BAA0B,CAAC,CAAC;gBACjD,CAAC;YACH,CAAC;YAAC,MAAM,CAAC;gBACP,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC,0BAA0B,CAAC,CAAC;YACjD,CAAC;QACH,CAAC;IACH,CAAC;IAEO,eAAe,CAAC,KAAa;QACnC,IAAI,KAAK,CAAC,QAAQ,CAAC,wBAAwB,CAAC;YAAE,OAAO,eAAe,CAAC;QACrE,IAAI,KAAK,CAAC,QAAQ,CAAC,mBAAmB,CAAC;YAAE,OAAO,iBAAiB,CAAC;QAClE,IAAI,KAAK,CAAC,QAAQ,CAAC,iBAAiB,CAAC;YAAE,OAAO,kBAAkB,CAAC;QACjE,IAAI,KAAK,CAAC,QAAQ,CAAC,MAAM,CAAC;YAAE,OAAO,YAAY,CAAC;QAChD,IAAI,KAAK,CAAC,QAAQ,CAAC,QAAQ,CAAC;YAAE,OAAO,cAAc,CAAC;QACpD,OAAO,OAAO,CAAC;IACjB,CAAC;IAEO,iBAAiB,CAAC,OAAe;QACvC,IAAI,OAAO,CAAC,QAAQ,CAAC,YAAY,CAAC;YAAE,OAAO,mBAAmB,CAAC;QAC/D,IAAI,OAAO,CAAC,QAAQ,CAAC,iBAAiB,CAAC;YAAE,OAAO,kBAAkB,CAAC;QACnE,OAAO,OAAO,CAAC;IACjB,CAAC;CACF;AAED,+EAA+E;AAC/E,4BAA4B;AAC5B,+EAA+E;AAE/E,MAAM,CAAC,MAAM,oBAAoB,GAAG,IAAI,cAAc,EAAE,CAAC"}