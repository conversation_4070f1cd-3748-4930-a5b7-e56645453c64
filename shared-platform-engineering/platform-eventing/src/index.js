/**
 * 🌟 Beautiful Eventing Platform
 *
 * Elegant event-driven architecture with NATS JetStream for Beauty CRM
 *
 * @example
 * ```typescript
 * import { NatsPublisher, createNatsPublisher } from '@beauty-crm/platform-eventing/nats';
 *
 * // Create a NATS publisher
 * const publisher = createNatsPublisher({
 *   natsUrl: 'nats://localhost:4222',
 *   streamName: 'APPOINTMENT_EVENTS',
 *   subjects: ['appointment.events.*'],
 *   serviceName: 'appointment-service'
 * });
 *
 * await publisher.connect();
 *
 * // Publish an event
 * await publisher.publish({
 *   eventType: 'appointment.created',
 *   aggregateId: 'appointment-123',
 *   aggregateType: 'appointment',
 *   data: { customerName: 'Alice', treatmentName: 'Facial' },
 *   metadata: {
 *     correlationId: 'req-456',
 *     source: 'appointment-service',
 *     timestamp: new Date().toISOString(),
 *     userId: 'user-123'
 *   }
 * });
 * ```
 */
// ============================================================================
// Core Classes
// ============================================================================
export { createPublisher, EventPublisher } from './EventPublisher';
export { createSubscriber, EventSubscriber } from './EventSubscriber';
export { createPrismaOutboxStorage } from './PrismaOutboxStorage';
// ============================================================================
// NATS Integration
// ============================================================================
export * from './nats';
// ============================================================================
// Outbox Pattern
// ============================================================================
export * from './outbox';
export { AggregateFactory, BaseAggregate, createAggregate, } from './SimpleAggregate';
export { domainEventToOutboxEntry, OutboxManager, outboxEntryToDomainEvent, } from './OutboxManager';
export { EventSchemaRegistry, globalEventSchemaRegistry, } from './EventSchemaRegistry';
export { EventValidator, globalEventValidator, } from './EventValidator';
export { appointmentCreatedSchemas, registerAppointmentSchemas, } from './schemas/AppointmentEventSchemas';
// ============================================================================
// Event Building
// ============================================================================
export { createDomainEvent, createEvent, createEventFor, EventBuilder, EventTypes, eventType, SubjectBuilder, Subjects, subject, } from './EventBuilder';
// ============================================================================
// Convenience Factory Functions
// ============================================================================
// Factory functions are exported from their respective files
// EventPublisher.ts exports createPublisher
// EventSubscriber.ts exports createSubscriber
// ============================================================================
// Common Stream Configurations
// ============================================================================
/**
 * 📋 Pre-configured streams for common use cases
 */
export const StreamConfigs = {
    /**
     * 🌍 All events stream (for monitoring/auditing)
     */
    all: {
        description: 'All domain events across the platform',
        maxAge: 30 * 24 * 60 * 60 * 1000 * 1000 * 1000,
        maxMessages: 100000,
        name: 'ALL_EVENTS', // 30 days
        subjects: ['*.events.*'],
    },
    /**
     * 📅 Appointment events stream
     */
    appointments: {
        description: 'All appointment lifecycle events',
        name: 'APPOINTMENT_EVENTS',
        subjects: ['appointment.events.*'],
    },
    /**
     * � Client events stream
     */
    clients: {
        description: 'All client management events',
        name: 'CLIENT_EVENTS',
        subjects: ['client.events.*'],
    },
    /**
     * 🏢 Salon events stream
     */
    salons: {
        description: 'All salon management events',
        name: 'SALON_EVENTS',
        subjects: ['salon.events.*'],
    },
    /**
     * � Staff events stream
     */
    staff: {
        description: 'All staff management events',
        name: 'STAFF_EVENTS',
        subjects: ['staff.events.*'],
    },
    /**
     * 🔄 Sync events stream
     */
    sync: {
        description: 'Cross-service synchronization events',
        name: 'SYNC_EVENTS',
        subjects: ['*.sync.*', '*.synced', '*.sync_failed'],
    },
    /**
     * 💆 Treatment events stream
     */
    treatments: {
        description: 'All treatment management events',
        name: 'TREATMENT_EVENTS',
        subjects: ['treatment.events.*'],
    },
};
// ============================================================================
// Common Publisher Configurations
// ============================================================================
/**
 * 📤 Pre-configured publishers for services
 */
export const PublisherConfigs = {
    appointment: (serviceName = 'appointment-service') => ({
        serviceName,
        stream: StreamConfigs.appointments,
    }),
    client: (serviceName = 'client-service') => ({
        serviceName,
        stream: StreamConfigs.clients,
    }),
    salon: (serviceName = 'salon-service') => ({
        serviceName,
        stream: StreamConfigs.salons,
    }),
    staff: (serviceName = 'staff-service') => ({
        serviceName,
        stream: StreamConfigs.staff,
    }),
    treatment: (serviceName = 'treatment-service') => ({
        serviceName,
        stream: StreamConfigs.treatments,
    }),
};
// ============================================================================
// Common Subscriber Configurations
// ============================================================================
/**
 * 📡 Pre-configured subscribers for services
 */
export const SubscriberConfigs = {
    appointment: (serviceName = 'appointment-subscriber') => ({
        consumer: {
            ackPolicy: 'explicit',
            deliverPolicy: 'new',
            name: `${serviceName}-consumer`,
        },
        serviceName,
    }),
    client: (serviceName = 'client-subscriber') => ({
        consumer: {
            ackPolicy: 'explicit',
            deliverPolicy: 'new',
            name: `${serviceName}-consumer`,
        },
        serviceName,
    }),
    salon: (serviceName = 'salon-subscriber') => ({
        consumer: {
            ackPolicy: 'explicit',
            deliverPolicy: 'new',
            name: `${serviceName}-consumer`,
        },
        serviceName,
    }),
    staff: (serviceName = 'staff-subscriber') => ({
        consumer: {
            ackPolicy: 'explicit',
            deliverPolicy: 'new',
            name: `${serviceName}-consumer`,
        },
        serviceName,
    }),
    treatment: (serviceName = 'treatment-subscriber') => ({
        consumer: {
            ackPolicy: 'explicit',
            deliverPolicy: 'new',
            name: `${serviceName}-consumer`,
        },
        serviceName,
    }),
};
// ============================================================================
// Version Information
// ============================================================================
export const version = '1.0.0';
export const name = '@beauty-crm/platform-eventing';
/**
 * 🎉 Welcome message
 */
export function welcome() {
    console.log(`
  🌟 Welcome to Beautiful Eventing Platform v${version}
  
  Elegant event-driven architecture with NATS JetStream
  Built with ❤️ for Beauty CRM
  
  Happy eventing! ✨
  `);
}
//# sourceMappingURL=index.js.map