{"version": 3, "file": "OutboxManager.js", "sourceRoot": "", "sources": ["OutboxManager.ts"], "names": [], "mappings": "AAAA;;;;;GAKG;AAEH,OAAO,EAAuB,oBAAoB,EAAE,MAAM,kBAAkB,CAAC;AAyG7E,+EAA+E;AAC/E,gCAAgC;AAChC,+EAA+E;AAE/E;;;;GAIG;AACH,MAAM,OAAO,aAAa;IACP,OAAO,CAAgB;IACvB,MAAM,CAGrB;IAEF,YAAY,OAAsB,EAAE,MAAoB;QACtD,IAAI,CAAC,OAAO,GAAG,OAAO,CAAC;QACvB,IAAI,CAAC,MAAM,GAAG;YACZ,SAAS,EAAE,GAAG;YACd,sBAAsB,EAAE,KAAK;YAC7B,UAAU,EAAE,CAAC;YACb,cAAc,EAAE,EAAE;YAClB,GAAG,MAAM;YACT,cAAc,EAAE,MAAM,CAAC,cAAc,IAAI,oBAAoB;SAC9D,CAAC;IACJ,CAAC;IAED,+EAA+E;IAC/E,gBAAgB;IAChB,+EAA+E;IAE/E;;;;;OAKG;IACH,KAAK,CAAC,WAAW,CACf,MAAqB,EACrB,WAAqB;QAErB,IAAI,MAAM,CAAC,MAAM,KAAK,CAAC;YAAE,OAAO;QAEhC,kDAAkD;QAClD,IAAI,IAAI,CAAC,MAAM,CAAC,sBAAsB,IAAI,IAAI,CAAC,MAAM,CAAC,cAAc,EAAE,CAAC;YACrE,KAAK,MAAM,KAAK,IAAI,MAAM,EAAE,CAAC;gBAC3B,IAAI,CAAC,MAAM,CAAC,cAAc,CAAC,iBAAiB,CAAC,KAAK,CAAC,CAAC;YACtD,CAAC;QACH,CAAC;QAED,MAAM,IAAI,CAAC,OAAO,CAAC,WAAW,CAAC,MAAM,EAAE,WAAW,CAAC,CAAC;IACtD,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,UAAU,CAAC,KAAkB,EAAE,WAAqB;QACxD,MAAM,IAAI,CAAC,WAAW,CAAC,CAAC,KAAK,CAAC,EAAE,WAAW,CAAC,CAAC;IAC/C,CAAC;IAED,+EAA+E;IAC/E,mBAAmB;IACnB,+EAA+E;IAE/E;;OAEG;IACH,KAAK,CAAC,oBAAoB,CAAC,KAAc;QACvC,MAAM,SAAS,GAAG,KAAK,IAAI,IAAI,CAAC,MAAM,CAAC,SAAS,CAAC;QACjD,OAAO,IAAI,CAAC,OAAO,CAAC,oBAAoB,CAAC,SAAS,CAAC,CAAC;IACtD,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,mBAAmB,CAAC,QAAkB;QAC1C,IAAI,QAAQ,CAAC,MAAM,KAAK,CAAC;YAAE,OAAO;QAClC,MAAM,IAAI,CAAC,OAAO,CAAC,mBAAmB,CAAC,QAAQ,CAAC,CAAC;IACnD,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,eAAe,CAAC,OAAe,EAAE,YAAoB;QACzD,MAAM,IAAI,CAAC,OAAO,CAAC,eAAe,CAAC,OAAO,EAAE,YAAY,CAAC,CAAC;IAC5D,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,mBAAmB,CAAC,OAAe;QACvC,MAAM,IAAI,CAAC,OAAO,CAAC,mBAAmB,CAAC,OAAO,CAAC,CAAC;IAClD,CAAC;IAED,+EAA+E;IAC/E,yBAAyB;IACzB,+EAA+E;IAE/E;;OAEG;IACH,KAAK,CAAC,sBAAsB;QAC1B,MAAM,UAAU,GAAG,IAAI,IAAI,EAAE,CAAC;QAC9B,UAAU,CAAC,QAAQ,CAAC,UAAU,CAAC,QAAQ,EAAE,GAAG,IAAI,CAAC,MAAM,CAAC,cAAc,CAAC,CAAC;QAExE,OAAO,IAAI,CAAC,OAAO,CAAC,sBAAsB,CAAC,UAAU,CAAC,CAAC;IACzD,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,cAAc;QAClB,OAAO,IAAI,CAAC,OAAO,CAAC,cAAc,EAAE,CAAC;IACvC,CAAC;IAED;;OAEG;IACH,SAAS;QACP,OAAO,IAAI,CAAC,MAAM,CAAC;IACrB,CAAC;CACF;AAED,+EAA+E;AAC/E,oBAAoB;AACpB,+EAA+E;AAE/E;;GAEG;AACH,MAAM,UAAU,wBAAwB,CACtC,KAAkB,EAClB,MAAc;IAYd,OAAO;QACL,WAAW,EAAE,KAAK,CAAC,WAAW;QAC9B,aAAa,EAAE,KAAK,CAAC,aAAa;QAClC,SAAS,EAAE,KAAK,CAAC,IAAI;QACrB,OAAO,EAAE,KAAK,CAAC,OAAO;QACtB,aAAa,EAAE,KAAK,CAAC,QAAQ;QAC7B,SAAS,EAAE,KAAK,CAAC,SAAS;QAC1B,YAAY,EAAE,KAAK,CAAC,YAAY;QAChC,MAAM,EAAE,MAAM;QACd,SAAS,EAAE,IAAI,IAAI,CAAC,KAAK,CAAC,SAAS,CAAC;KACrC,CAAC;AACJ,CAAC;AAED;;GAEG;AACH,MAAM,UAAU,wBAAwB,CAAC,KAAkB;IACzD,OAAO;QACL,WAAW,EAAE,KAAK,CAAC,WAAW;QAC9B,aAAa,EAAE,KAAK,CAAC,aAAa;QAClC,IAAI,EAAE,KAAK,CAAC,SAAS;QACrB,OAAO,EAAE,KAAK,CAAC,OAAO;QACtB,SAAS,EAAE,KAAK,CAAC,SAAS;QAC1B,YAAY,EAAE,KAAK,CAAC,YAAY;QAChC,QAAQ,EAAE,KAAK,CAAC,aAAoD;QACpE,MAAM,EAAE,KAAK,CAAC,MAAM;QACpB,SAAS,EAAE,KAAK,CAAC,SAAS,CAAC,WAAW,EAAE;KACzC,CAAC;AACJ,CAAC"}