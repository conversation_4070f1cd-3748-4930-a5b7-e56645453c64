/**
 * 🗄️ Prisma Outbox Storage
 *
 * Prisma implementation of OutboxStorage for transactional outbox pattern
 */
import { randomUUID } from 'node:crypto';
import { domainEventToOutboxEntry } from './OutboxManager';
// ============================================================================
// Prisma Outbox Storage Implementation
// ============================================================================
/**
 * 🗄️ Prisma Outbox Storage
 *
 * Implements OutboxStorage using Prisma ORM for database operations
 */
export class PrismaOutboxStorage {
    outboxModel;
    prisma;
    source;
    constructor(prisma, source) {
        this.prisma = prisma;
        this.outboxModel = prisma.transactionalOutbox;
        this.source = source;
    }
    // ============================================================================
    // Event Storage
    // ============================================================================
    /**
     * 💾 Store events atomically in the outbox table
     */
    async storeEvents(events, transaction) {
        if (events.length === 0)
            return;
        const client = transaction || this.prisma;
        const outboxModel = client.transactionalOutbox;
        const outboxEntries = events.map((event) => {
            const entry = domainEventToOutboxEntry(event, this.source);
            return {
                id: randomUUID(),
                ...entry,
                processed: false,
                retryCount: 0,
            };
        });
        if (outboxEntries.length === 1) {
            await outboxModel.create({
                data: outboxEntries[0],
            });
        }
        else {
            await outboxModel.createMany({
                data: outboxEntries,
            });
        }
    }
    // ============================================================================
    // Event Retrieval
    // ============================================================================
    /**
     * 📤 Retrieve unprocessed events for publishing
     */
    async getUnprocessedEvents(limit = 100) {
        const entries = await this.outboxModel.findMany({
            orderBy: {
                createdAt: 'asc',
            },
            take: limit,
            where: {
                processed: false,
            },
        });
        return entries.map(this.mapToOutboxEntry);
    }
    // ============================================================================
    // Event Status Updates
    // ============================================================================
    /**
     * ✅ Mark events as processed
     */
    async markEventsProcessed(eventIds) {
        if (eventIds.length === 0)
            return;
        await this.outboxModel.updateMany({
            data: {
                processed: true,
                processedAt: new Date(),
            },
            where: {
                eventId: {
                    in: eventIds,
                },
            },
        });
    }
    /**
     * ❌ Mark event as failed with error message
     */
    async markEventFailed(eventId, errorMessage) {
        await this.outboxModel.update({
            data: {
                errorMessage,
                lastRetryAt: new Date(),
            },
            where: {
                eventId,
            },
        });
    }
    /**
     * 🔄 Increment retry count for failed events
     */
    async incrementRetryCount(eventId) {
        await this.outboxModel.update({
            data: {
                retryCount: {
                    increment: 1,
                },
            },
            where: {
                eventId,
            },
        });
    }
    // ============================================================================
    // Maintenance Operations
    // ============================================================================
    /**
     * 🧹 Clean up processed events older than specified date
     */
    async cleanupProcessedEvents(olderThan) {
        const result = await this.outboxModel.deleteMany({
            where: {
                processed: true,
                processedAt: {
                    lt: olderThan,
                },
            },
        });
        return result.count;
    }
    // ============================================================================
    // Statistics and Monitoring
    // ============================================================================
    /**
     * 📊 Get outbox statistics for monitoring
     */
    async getOutboxStats() {
        const [totalEvents, unprocessedEvents, processedEvents, failedEvents, oldestUnprocessed, newestEvent,] = await Promise.all([
            this.outboxModel.count(),
            this.outboxModel.count({
                where: { processed: false },
            }),
            this.outboxModel.count({
                where: { processed: true },
            }),
            this.outboxModel.count({
                where: {
                    processed: false,
                    retryCount: { gt: 0 },
                },
            }),
            this.outboxModel.aggregate({
                // biome-ignore lint/style/useNamingConvention: Prisma aggregate fields
                _min: { createdAt: true },
                where: { processed: false },
            }),
            this.outboxModel.aggregate({
                // biome-ignore lint/style/useNamingConvention: Prisma aggregate fields
                _max: { createdAt: true },
            }),
        ]);
        return {
            failedEvents,
            newestEvent: newestEvent._max?.createdAt || undefined,
            oldestUnprocessedEvent: oldestUnprocessed._min?.createdAt || undefined,
            processedEvents,
            totalEvents,
            unprocessedEvents,
        };
    }
    // ============================================================================
    // Helper Methods
    // ============================================================================
    /**
     * 🔄 Map database entry to OutboxEntry interface
     */
    mapToOutboxEntry(entry) {
        return {
            aggregateId: entry.aggregateId,
            aggregateType: entry.aggregateType,
            createdAt: entry.createdAt,
            errorMessage: entry.errorMessage,
            eventData: entry.eventData,
            eventId: entry.eventId,
            eventMetadata: entry.eventMetadata,
            eventType: entry.eventType,
            eventVersion: entry.eventVersion,
            id: entry.id,
            lastRetryAt: entry.lastRetryAt,
            processed: entry.processed,
            processedAt: entry.processedAt,
            retryCount: entry.retryCount,
            source: entry.source,
            timestamp: entry.timestamp,
            updatedAt: entry.updatedAt,
        };
    }
}
// ============================================================================
// Factory Functions
// ============================================================================
/**
 * 🏭 Create Prisma Outbox Storage instance
 */
export function createPrismaOutboxStorage(prisma, source) {
    return new PrismaOutboxStorage(prisma, source);
}
//# sourceMappingURL=PrismaOutboxStorage.js.map