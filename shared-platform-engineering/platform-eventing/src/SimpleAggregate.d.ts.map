{"version": 3, "file": "SimpleAggregate.d.ts", "sourceRoot": "", "sources": ["SimpleAggregate.ts"], "names": [], "mappings": "AAAA;;;;GAIG;AAEH,OAAO,EAAe,UAAU,EAAE,MAAM,gBAAgB,CAAC;AACzD,OAAO,KAAK,EAAE,aAAa,EAAE,MAAM,iBAAiB,CAAC;AACrD,OAAO,KAAK,EACV,OAAO,EACP,aAAa,EACb,WAAW,EACX,aAAa,EACb,eAAe,EAChB,MAAM,SAAS,CAAC;AAEjB;;;;;GAKG;AACH,8BAAsB,aAAa,CAAC,MAAM,GAAG,OAAO,CAClD,YAAW,eAAe,CAAC,MAAM,CAAC;IAElC,SAAgB,EAAE,EAAE,MAAM,CAAC;IACpB,KAAK,EAAE,MAAM,GAAG,IAAI,CAAQ;IACnC,SAAS,CAAC,OAAO,SAAK;IACtB,SAAS,CAAC,aAAa,CAAC,EAAE,aAAa,CAAC;gBAGtC,EAAE,EAAE,MAAM,EACV,YAAY,CAAC,EAAE,MAAM,EACrB,aAAa,CAAC,EAAE,aAAa;IAW/B;;OAEG;IACH,QAAQ,CAAC,cAAc,CAAC,OAAO,EAAE,OAAO,GAAG,WAAW,EAAE;IAExD;;OAEG;IACI,wBAAwB,CAAC,OAAO,EAAE,OAAO,GAAG,aAAa;IAqBhE;;;;;OAKG;IACU,wBAAwB,CACnC,OAAO,EAAE,OAAO,EAChB,WAAW,CAAC,EAAE,OAAO,GACpB,OAAO,CAAC,aAAa,CAAC;IA8BzB;;OAEG;IACH,SAAS,CAAC,WAAW,CAAC,KAAK,EACzB,SAAS,EAAE,MAAM,EACjB,IAAI,EAAE,KAAK,EACX,OAAO,CAAC,EAAE;QACR,aAAa,CAAC,EAAE,MAAM,CAAC;QACvB,WAAW,CAAC,EAAE,MAAM,CAAC;QACrB,MAAM,CAAC,EAAE,MAAM,CAAC;QAChB,QAAQ,CAAC,EAAE,aAAa,CAAC;KAC1B,GACA,WAAW,CAAC,KAAK,CAAC;IAyBrB;;OAEG;IACH,SAAS,CAAC,sBAAsB,CAAC,KAAK,EACpC,SAAS,EAAE,MAAM,EACjB,IAAI,EAAE,KAAK,EACX,OAAO,EAAE,OAAO,GACf,WAAW,CAAC,KAAK,CAAC;IAWrB;;OAEG;IACH,SAAS,CAAC,gBAAgB,CAAC,KAAK,EAC9B,MAAM,EAAE,MAAM,OAAO,UAAU,EAC/B,IAAI,EAAE,KAAK,EACX,OAAO,CAAC,EAAE;QACR,aAAa,CAAC,EAAE,MAAM,CAAC;QACvB,WAAW,CAAC,EAAE,MAAM,CAAC;QACrB,MAAM,CAAC,EAAE,MAAM,CAAC;QAChB,QAAQ,CAAC,EAAE,aAAa,CAAC;KAC1B,GACA,WAAW,CAAC,KAAK,CAAC;IASrB;;OAEG;IACH,SAAS,CAAC,QAAQ,CAAC,gBAAgB,IAAI,MAAM;IAE7C;;OAEG;IACH,SAAS,CAAC,QAAQ,CAAC,SAAS,IAAI,MAAM;IAMtC;;OAEG;IACI,UAAU,IAAI,MAAM;IAI3B;;OAEG;IACH,SAAS,CAAC,gBAAgB,CACxB,YAAY,EAAE,CAAC,KAAK,EAAE,MAAM,GAAG,IAAI,KAAK,MAAM,GAC7C,IAAI;IAIP;;OAEG;IACI,cAAc,CAAC,OAAO,EAAE,MAAM,GAAG,IAAI;IAI5C;;OAEG;IACI,gBAAgB,CAAC,aAAa,EAAE,aAAa,GAAG,IAAI;IAQ3D;;OAEG;IACH,SAAS,CAAC,eAAe,CAAC,OAAO,EAAE,OAAO,GAAG,IAAI;IASjD;;OAEG;IACH,SAAS,CAAC,gBAAgB,CAAC,OAAO,EAAE,MAAM,EAAE,IAAI,CAAC,EAAE,MAAM,GAAG,KAAK;CAOlE;AAMD;;GAEG;AACH,MAAM,MAAM,cAAc,CAAC,CAAC,IAAI,CAAC,SAAS,aAAa,CAAC,MAAM,CAAC,CAAC,GAAG,CAAC,GAAG,KAAK,CAAC;AAE7E;;GAEG;AACH,MAAM,MAAM,oBAAoB,CAAC,CAAC,SAAS,aAAa,GAAG,aAAa,IACtE,KACE,EAAE,EAAE,MAAM,EACV,YAAY,CAAC,EAAE,cAAc,CAAC,CAAC,CAAC,EAChC,aAAa,CAAC,EAAE,aAAa,KAC1B,CAAC,CAAC;AAMT;;GAEG;AACH,eAAO,MAAM,gBAAgB;IAC3B;;OAEG;WACI,CAAC,SAAS,aAAa,kBACZ,oBAAoB,CAAC,CAAC,CAAC,MACnC,MAAM,iBACK,cAAc,CAAC,CAAC,CAAC,kBAChB,aAAa,GAC5B,CAAC;IAIJ;;OAEG;YACK,CAAC,SAAS,aAAa,kBACb,oBAAoB,CAAC,CAAC,CAAC,MACnC,MAAM,SACH,cAAc,CAAC,CAAC,CAAC,oCAER,aAAa,GAC5B,CAAC;CAML,CAAC;AAEF;;GAEG;AACH,wBAAgB,eAAe,CAAC,CAAC,SAAS,aAAa,EACrD,cAAc,EAAE,oBAAoB,CAAC,CAAC,CAAC,EACvC,EAAE,EAAE,MAAM,EACV,YAAY,CAAC,EAAE,cAAc,CAAC,CAAC,CAAC,EAChC,aAAa,CAAC,EAAE,aAAa,GAC5B,CAAC,CAOH"}