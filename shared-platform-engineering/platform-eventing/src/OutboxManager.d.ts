/**
 * 📦 Outbox Manager
 *
 * Transactional outbox pattern implementation for reliable event publishing
 * Provides atomic event storage and retrieval for Debezium CDC integration
 */
import { type EventValidator } from './EventValidator';
import type { DomainEvent } from './types';
/**
 * Outbox entry structure matching the database schema
 */
export interface OutboxEntry {
    id: string;
    aggregateId: string;
    aggregateType: string;
    eventType: string;
    eventId: string;
    eventVersion: number;
    eventData: unknown;
    eventMetadata?: unknown;
    source: string;
    timestamp: Date;
    processed: boolean;
    processedAt?: Date;
    retryCount: number;
    lastRetryAt?: Date;
    errorMessage?: string;
    createdAt: Date;
    updatedAt: Date;
}
/**
 * Outbox storage interface for different database implementations
 */
export interface OutboxStorage {
    /**
     * Store events atomically in outbox table
     */
    storeEvents(events: DomainEvent[], transaction?: unknown): Promise<void>;
    /**
     * Get unprocessed events for publishing
     */
    getUnprocessedEvents(limit: number): Promise<OutboxEntry[]>;
    /**
     * Mark events as processed
     */
    markEventsProcessed(eventIds: string[]): Promise<void>;
    /**
     * Mark event as failed with error message
     */
    markEventFailed(eventId: string, errorMessage: string): Promise<void>;
    /**
     * Increment retry count for failed events
     */
    incrementRetryCount(eventId: string): Promise<void>;
    /**
     * Clean up processed events older than specified date
     */
    cleanupProcessedEvents(olderThan: Date): Promise<number>;
    /**
     * Get outbox statistics for monitoring
     */
    getOutboxStats(): Promise<OutboxStats>;
}
/**
 * Outbox statistics for monitoring
 */
export interface OutboxStats {
    totalEvents: number;
    unprocessedEvents: number;
    processedEvents: number;
    failedEvents: number;
    oldestUnprocessedEvent?: Date;
    newestEvent?: Date;
}
/**
 * Outbox configuration options
 */
export interface OutboxConfig {
    /** Source service name */
    source: string;
    /** Maximum retry attempts for failed events */
    maxRetries?: number;
    /** Batch size for processing events */
    batchSize?: number;
    /** Retention period for processed events (in hours) */
    retentionHours?: number;
    /** Enable event schema validation before storing */
    enableSchemaValidation?: boolean;
    /** Event validator instance (uses global validator if not provided) */
    eventValidator?: EventValidator;
}
/**
 * 📦 Outbox Manager
 *
 * Manages transactional outbox operations for reliable event publishing
 */
export declare class OutboxManager {
    private readonly storage;
    private readonly config;
    constructor(storage: OutboxStorage, config: OutboxConfig);
    /**
     * 💾 Store events atomically in outbox
     *
     * This method should be called within the same database transaction
     * as the business logic to ensure atomicity
     */
    storeEvents(events: DomainEvent[], transaction?: unknown): Promise<void>;
    /**
     * 💾 Store single event in outbox
     */
    storeEvent(event: DomainEvent, transaction?: unknown): Promise<void>;
    /**
     * 📤 Get unprocessed events for publishing
     */
    getUnprocessedEvents(limit?: number): Promise<OutboxEntry[]>;
    /**
     * ✅ Mark events as successfully processed
     */
    markEventsProcessed(eventIds: string[]): Promise<void>;
    /**
     * ❌ Mark event as failed with error message
     */
    markEventFailed(eventId: string, errorMessage: string): Promise<void>;
    /**
     * 🔄 Increment retry count for failed events
     */
    incrementRetryCount(eventId: string): Promise<void>;
    /**
     * 🧹 Clean up processed events older than retention period
     */
    cleanupProcessedEvents(): Promise<number>;
    /**
     * 📊 Get outbox statistics for monitoring
     */
    getOutboxStats(): Promise<OutboxStats>;
    /**
     * 🔍 Get configuration for debugging
     */
    getConfig(): Readonly<typeof this.config>;
}
/**
 * 🔄 Convert DomainEvent to OutboxEntry
 */
export declare function domainEventToOutboxEntry(event: DomainEvent, source: string): Omit<OutboxEntry, 'id' | 'processed' | 'processedAt' | 'retryCount' | 'lastRetryAt' | 'errorMessage' | 'createdAt' | 'updatedAt'>;
/**
 * 🔄 Convert OutboxEntry to DomainEvent
 */
export declare function outboxEntryToDomainEvent(entry: OutboxEntry): DomainEvent;
//# sourceMappingURL=OutboxManager.d.ts.map