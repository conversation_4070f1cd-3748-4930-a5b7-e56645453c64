/**
 * 📅 Appointment Event Schemas
 *
 * Defines versioned schemas for appointment-related events with proper
 * validation, compatibility, and evolution support.
 */
import { type EventSchema } from '../EventSchemaRegistry';
export interface AppointmentCreatedEventDataV1 {
  appointmentId: string;
  customerId: string;
  staffId: string;
  treatmentId: string;
  salonId: string;
  scheduledAt: string;
  duration: number;
  status: 'SCHEDULED';
  notes?: string;
  metadata: {
    source: string;
    createdBy?: string;
    clientVersion?: string;
  };
}
export interface AppointmentCreatedEventDataV2 {
  appointmentId: string;
  customerId: string;
  staffId: string;
  treatmentId: string;
  salonId: string;
  scheduledAt: string;
  duration: number;
  status: 'SCHEDULED';
  notes?: string;
  pricing: {
    basePrice: number;
    currency: string;
    discounts?: Array<{
      type: string;
      amount: number;
      reason?: string;
    }>;
  };
  metadata: {
    source: string;
    createdBy?: string;
    clientVersion?: string;
    bookingChannel?: 'web' | 'mobile' | 'phone' | 'walk-in';
  };
}
export declare const appointmentCreatedSchemas: EventSchema[];
export declare function registerAppointmentSchemas(): void;
//# sourceMappingURL=AppointmentEventSchemas.d.ts.map
