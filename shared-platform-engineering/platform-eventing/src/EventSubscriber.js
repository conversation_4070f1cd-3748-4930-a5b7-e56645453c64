/**
 * 📡 EventSubscriber
 *
 * Elegant, type-safe event subscription with NATS JetStream
 */
import { connect, JSONCodec, AckPolicy, // Import ConsumerConfig
DeliverPolicy, // Import DeliverPolicy
 } from 'nats';
/**
 * 📡 Beautiful Event Subscriber
 *
 * Elegant subscription to domain events with NATS JetStream
 */
export class EventSubscriber {
    connection = null;
    jetStream = null;
    jsonCodec = JSONCodec();
    config;
    status = 'disconnected';
    subscriptions = new Map(); // Allow JetStreamSubscription
    handlers = new Map();
    errorHandler;
    constructor(config) {
        this.config = {
            connection: {
                maxReconnectAttempts: 10,
                reconnectTimeWait: 2000,
                ...config.connection,
            },
            consumer: {
                ackPolicy: 'explicit',
                ackWait: 30000,
                deliverPolicy: 'new',
                maxDeliver: 3,
                ...config.consumer,
            },
            natsUrl: config.natsUrl,
            servers: config.servers || [config.natsUrl || 'nats://localhost:4222'],
            serviceName: config.serviceName,
        };
    }
    /**
     * 🔌 Connect to NATS
     */
    async connect() {
        try {
            this.status = 'connecting';
            this.connection = await connect({
                maxPingOut: 3,
                maxReconnectAttempts: this.config.connection.maxReconnectAttempts,
                pedantic: false,
                pingInterval: 30000,
                reconnect: true,
                reconnectTimeWait: this.config.connection.reconnectTimeWait,
                servers: this.config.servers,
                timeout: 15000,
                verbose: false,
                waitOnFirstConnect: true,
            });
            this.jetStream = this.connection.jetstream();
            this.status = 'connected';
            console.log(`🎯 EventSubscriber connected to NATS (${this.config.serviceName})`);
        }
        catch (error) {
            this.status = 'error';
            console.error('💥 Failed to connect to NATS:', error);
            throw error;
        }
    }
    /**
     * 📨 Subscribe to regular NATS subject
     */
    async subscribe(subject, handler) {
        if (!this.connection) {
            throw new Error('🚫 Not connected to NATS. Call connect() first!');
        }
        try {
            const subscription = this.connection.subscribe(subject);
            this.subscriptions.set(subject, subscription);
            this.handlers.set(subject, handler);
            this.processMessages(subject, subscription);
            console.log(`📡 Subscribed to ${subject}`);
        }
        catch (error) {
            console.error(`💥 Failed to subscribe to ${subject}:`, error);
            throw error;
        }
    }
    /**
     * 🌊 Subscribe to JetStream
     */
    async subscribeToStream(streamName, subject, handler, options) {
        if (!this.connection) {
            throw new Error('🚫 Not connected to NATS. Call connect() first!');
        }
        try {
            const jsm = await this.connection.jetstreamManager();
            const consumerConfig = {
                durable_name: options?.consumerName || `${this.config.serviceName}-${streamName}`,
                ack_policy: options?.ackPolicy
                    ? this.mapAckPolicy(options.ackPolicy)
                    : AckPolicy.Explicit,
                deliver_policy: options?.deliverPolicy
                    ? this.mapDeliverPolicy(options.deliverPolicy)
                    : undefined,
                // Add other relevant options from SubscriptionOptions to ConsumerConfig
            };
            // Ensure the consumer exists (add or update)
            await jsm.consumers.add(streamName, consumerConfig);
            // Get the consumer and subscribe
            // const consumer = await this.jetStream!.consumers.get(
            //   streamName,
            //   consumerConfig.durable_name,
            // );
            // Use the consume method for push-like delivery
            // The consume method takes options as the first argument and the callback as the second.
            // const subscription = consumer.consume(
            //   {
            //     /* ConsumeOptions if needed */
            //   }, // Placeholder for options
            //   (msg: JsMsg) => {
            //     try {
            //       const event = this.jsonCodec.decode(msg.data) as T; // Cast to T
            //       handler(event);
            //       msg.ack();
            //       console.log(
            //         `✅ Processed and acknowledged ${event.eventType} event from ${key}`,
            //       );
            //     } catch (error) {
            //       console.error(`💥 Failed to process message from ${key}:`, error);
            //       msg.nak();
            //       if (this.errorHandler) {
            //         this.errorHandler(error as Error);
            //       }
            //     }
            //   },
            // );
            const key = `${streamName}:${subject}`;
            // consumer.consume() does not return a standard Subscription or JetStreamSubscription
            // We might need a different way to track these or adjust the subscriptions map type.
            // For now, we won't add it to the map to resolve the type error.
            // this.subscriptions.set(key, subscription);
            this.handlers.set(key, handler);
            console.log(`🌊 Subscribed to stream ${streamName} subject ${subject}`);
        }
        catch (error) {
            console.error(`💥 Failed to subscribe to stream ${streamName} subject ${subject}:`, error);
            throw error;
        }
    }
    /**
     * 🚨 Set error handler
     */
    onError(handler) {
        this.errorHandler = handler;
    }
    /**
     * 🔄 Process regular NATS messages
     */
    async processMessages(subject, subscription) {
        const handler = this.handlers.get(subject);
        if (!handler)
            return;
        (async () => {
            for await (const msg of subscription) {
                try {
                    const event = this.jsonCodec.decode(msg.data);
                    await handler(event);
                    console.log(`✅ Processed ${event.eventType} event from ${subject}`);
                }
                catch (error) {
                    console.error(`💥 Failed to process message from ${subject}:`, error);
                    if (this.errorHandler) {
                        await this.errorHandler(error);
                    }
                }
            }
        })().catch(async (err) => {
            console.error(`💥 Subscription error for ${subject}:`, err);
            if (this.errorHandler) {
                await this.errorHandler(err);
            }
        });
    }
    /**
     * 🔌 Disconnect from NATS
     */
    async disconnect() {
        if (!this.connection)
            return;
        const closePromise = this.connection.closed();
        for (const [key, subscription] of this.subscriptions) {
            try {
                subscription.unsubscribe();
                console.log(`🔇 Unsubscribed from ${key}`);
            }
            catch (error) {
                console.error(`💥 Failed to unsubscribe from ${key}:`, error);
            }
        }
        await this.connection.close();
        const err = await closePromise;
        if (err) {
            console.error('💥 Connection closed with error:', err);
        }
        else {
            console.log('👋 Disconnected from NATS');
        }
    }
    /**
     * 🏥 Health check
     */
    async healthCheck() {
        if (!this.connection || !this.connection.isClosed()) {
            return {
                details: {
                    error: 'Not connected to NATS',
                },
                healthy: false,
                status: this.status,
            };
        }
        try {
            return {
                details: {
                    lastSuccess: new Date().toISOString(),
                    subscriptions: Array.from(this.subscriptions.keys()),
                },
                healthy: this.status === 'connected',
                status: this.status,
            };
        }
        catch (error) {
            return {
                details: {
                    error: error.message,
                },
                healthy: false,
                status: this.status,
            };
        }
    }
    /**
     * 📊 Get connection status
     */
    getStatus() {
        return this.status;
    }
    /**
     * 🔍 Check if connected
     */
    isConnected() {
        return (this.status === 'connected' &&
            this.connection !== null &&
            !this.connection.isClosed());
    }
    /**
     * 📋 Get active subscriptions
     */
    getSubscriptions() {
        return Array.from(this.subscriptions.keys());
    }
    /**
     * Maps local AckPolicy string literal to NATS AckPolicy enum.
     */
    mapAckPolicy(ackPolicy) {
        switch (ackPolicy) {
            case 'none':
                return AckPolicy.None;
            case 'all':
                return AckPolicy.All;
            case 'explicit':
                return AckPolicy.Explicit;
            default:
                // This case should ideally not be reached if the type is correct,
                // but as a fallback, return Explicit.
                return AckPolicy.Explicit;
        }
    }
    /**
     * Maps local DeliverPolicy string literal to NATS DeliverPolicy type.
     */
    mapDeliverPolicy(deliverPolicy) {
        switch (deliverPolicy) {
            case 'all':
                return DeliverPolicy.All;
            case 'last':
                return DeliverPolicy.Last;
            case 'new':
                return DeliverPolicy.New;
            case 'by_start_sequence':
                return DeliverPolicy.StartSequence;
            case 'by_start_time':
                return DeliverPolicy.StartTime;
            default:
                // Fallback, though ideally covered by type
                return DeliverPolicy.New;
        }
    }
}
// ============================================================================
// Factory Functions
// ============================================================================
/**
 * 🏭 Create a new EventSubscriber with configuration
 */
export function createSubscriber(config) {
    return new EventSubscriber({
        consumer: config.consumer,
        natsUrl: config.natsUrl || 'nats://localhost:4222',
        serviceName: config.serviceName,
    });
}
//# sourceMappingURL=EventSubscriber.js.map