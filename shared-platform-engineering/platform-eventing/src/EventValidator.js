/**
 * 🔍 Event Validator
 *
 * Provides event validation utilities that integrate with the schema registry
 * and domain event system for reliable event processing.
 */
import { globalEventSchemaRegistry, } from './EventSchemaRegistry';
export const DEFAULT_VALIDATION_CONFIG = {
    allowUnknownEvents: true,
    logValidationErrors: true,
    strictMode: false,
};
// ============================================================================
// Event Validator
// ============================================================================
export class EventValidator {
    schemaRegistry;
    config;
    constructor(schemaRegistry = globalEventSchemaRegistry, config = DEFAULT_VALIDATION_CONFIG) {
        this.schemaRegistry = schemaRegistry;
        this.config = { ...DEFAULT_VALIDATION_CONFIG, ...config };
    }
    /**
     * Validate a single domain event
     */
    validateEvent(event) {
        const startTime = Date.now();
        const result = {
            errors: [],
            eventType: event.eventType,
            metadata: {
                schemaFound: false,
                timestamp: new Date().toISOString(),
                validationTime: 0,
            },
            valid: false,
            version: event.eventVersion || 1,
            warnings: [],
        };
        try {
            // Check if schema exists
            const hasSchema = this.schemaRegistry.hasEventType(event.eventType);
            result.metadata.schemaFound = hasSchema;
            if (!hasSchema) {
                if (this.config.allowUnknownEvents) {
                    result.valid = true;
                    result.warnings.push(`No schema found for event type: ${event.eventType}`);
                }
                else {
                    result.errors.push(`No schema found for event type: ${event.eventType}`);
                }
            }
            else {
                // Validate against schema
                const schemaResult = this.schemaRegistry.validateEvent(event.eventType, event.data, event.eventVersion || this.config.defaultVersion);
                result.valid = schemaResult.valid;
                result.version = schemaResult.version;
                if (schemaResult.errors) {
                    result.errors.push(...schemaResult.errors);
                }
                if (schemaResult.warnings) {
                    result.warnings.push(...schemaResult.warnings);
                }
                // Apply strict mode
                if (this.config.strictMode && result.warnings.length > 0) {
                    result.valid = false;
                    result.errors.push('Strict mode: warnings treated as errors');
                }
            }
            // Validate event structure
            this.validateEventStructure(event, result);
        }
        catch (error) {
            result.valid = false;
            result.errors.push(`Validation error: ${error instanceof Error ? error.message : 'Unknown error'}`);
        }
        result.metadata.validationTime = Date.now() - startTime;
        // Log validation errors if configured
        if (this.config.logValidationErrors && !result.valid) {
            console.error('Event validation failed:', {
                errors: result.errors,
                eventId: event.eventId,
                eventType: event.eventType,
                warnings: result.warnings,
            });
        }
        return result;
    }
    /**
     * Validate multiple events in batch
     */
    validateEvents(events) {
        const startTime = Date.now();
        const results = [];
        const errorsByType = {};
        const warningsByType = {};
        for (const event of events) {
            const result = this.validateEvent(event);
            results.push(result);
            // Count errors by type
            for (const error of result.errors) {
                const errorType = this.categorizeError(error);
                errorsByType[errorType] = (errorsByType[errorType] || 0) + 1;
            }
            // Count warnings by type
            for (const warning of result.warnings) {
                const warningType = this.categorizeWarning(warning);
                warningsByType[warningType] = (warningsByType[warningType] || 0) + 1;
            }
        }
        const validEvents = results.filter((r) => r.valid).length;
        const invalidEvents = results.length - validEvents;
        return {
            invalidEvents,
            results,
            summary: {
                errorsByType,
                validationTime: Date.now() - startTime,
                warningsByType,
            },
            totalEvents: events.length,
            validEvents,
        };
    }
    /**
     * Validate event before publishing to outbox
     */
    validateForOutbox(event) {
        const result = this.validateEvent(event);
        if (!result.valid) {
            throw new Error(`Event validation failed for outbox storage: ${result.errors.join(', ')}`);
        }
        if (this.config.strictMode && result.warnings.length > 0) {
            throw new Error(`Event validation warnings in strict mode: ${result.warnings.join(', ')}`);
        }
    }
    /**
     * Get validation statistics
     */
    getValidationStats(results) {
        const totalEvents = results.length;
        const validEvents = results.filter((r) => r.valid).length;
        const successRate = totalEvents > 0 ? (validEvents / totalEvents) * 100 : 0;
        const totalValidationTime = results.reduce((sum, r) => sum + r.metadata.validationTime, 0);
        const averageValidationTime = totalEvents > 0 ? totalValidationTime / totalEvents : 0;
        // Count errors
        const errorCounts = {};
        for (const result of results) {
            for (const error of result.errors) {
                errorCounts[error] = (errorCounts[error] || 0) + 1;
            }
        }
        const mostCommonErrors = Object.entries(errorCounts)
            .map(([error, count]) => ({ count, error }))
            .sort((a, b) => b.count - a.count)
            .slice(0, 10);
        // Event type breakdown
        const eventTypeBreakdown = {};
        for (const result of results) {
            if (!eventTypeBreakdown[result.eventType]) {
                eventTypeBreakdown[result.eventType] = { invalid: 0, valid: 0 };
            }
            if (result.valid) {
                eventTypeBreakdown[result.eventType].valid++;
            }
            else {
                eventTypeBreakdown[result.eventType].invalid++;
            }
        }
        return {
            averageValidationTime,
            eventTypeBreakdown,
            mostCommonErrors,
            successRate,
        };
    }
    // ============================================================================
    // Private Methods
    // ============================================================================
    validateEventStructure(event, result) {
        // Validate required DomainEvent fields
        if (!event.eventId) {
            result.errors.push('Missing required field: eventId');
        }
        if (!event.eventType) {
            result.errors.push('Missing required field: eventType');
        }
        if (!event.aggregateId) {
            result.errors.push('Missing required field: aggregateId');
        }
        if (!event.aggregateType) {
            result.errors.push('Missing required field: aggregateType');
        }
        if (!event.timestamp) {
            result.errors.push('Missing required field: timestamp');
        }
        if (event.eventVersion !== undefined && event.eventVersion < 1) {
            result.errors.push('eventVersion must be >= 1');
        }
        // Validate timestamp format
        if (event.timestamp) {
            try {
                const date = new Date(event.timestamp);
                if (Number.isNaN(date.getTime())) {
                    result.errors.push('Invalid timestamp format');
                }
            }
            catch {
                result.errors.push('Invalid timestamp format');
            }
        }
    }
    categorizeError(error) {
        if (error.includes('Missing required field'))
            return 'missing-field';
        if (error.includes('Invalid timestamp'))
            return 'timestamp-error';
        if (error.includes('No schema found'))
            return 'schema-not-found';
        if (error.includes('type'))
            return 'type-error';
        if (error.includes('format'))
            return 'format-error';
        return 'other';
    }
    categorizeWarning(warning) {
        if (warning.includes('deprecated'))
            return 'deprecated-schema';
        if (warning.includes('No schema found'))
            return 'schema-not-found';
        return 'other';
    }
}
// ============================================================================
// Global Validator Instance
// ============================================================================
export const globalEventValidator = new EventValidator();
//# sourceMappingURL=EventValidator.js.map