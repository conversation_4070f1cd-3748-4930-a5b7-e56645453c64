/**
 * 🏗️ Beautiful Event Builder
 *
 * Fluent API for creating domain events with elegance
 */
/**
 * 🌟 Beautiful Event Builder
 *
 * Create domain events with a delightful fluent API
 */
export class EventBuilder {
    event = {
        eventVersion: 1,
        metadata: {},
        timestamp: new Date().toISOString(),
    };
    /**
     * 🆔 Set the event ID (auto-generated if not provided)
     */
    id(eventId) {
        this.event.eventId = eventId;
        return this;
    }
    /**
     * 🏷️ Set the event type
     */
    type(eventType) {
        this.event.eventType = eventType;
        return this;
    }
    /**
     * 🎯 Set the aggregate information
     */
    aggregate(aggregateId, aggregateType) {
        this.event.aggregateId = aggregateId;
        this.event.aggregateType = aggregateType;
        return this;
    }
    /**
     * 📦 Set the event data
     */
    data(data) {
        this.event.data = data;
        return this;
    }
    /**
     * 🏢 Set the source service
     */
    source(source) {
        this.event.source = source;
        return this;
    }
    /**
     * 📅 Set the timestamp (defaults to now)
     */
    timestamp(timestamp) {
        this.event.timestamp =
            timestamp instanceof Date ? timestamp.toISOString() : timestamp;
        return this;
    }
    /**
     * 🔢 Set the event version
     */
    version(version) {
        this.event.eventVersion = version;
        return this;
    }
    /**
     * 🔗 Set correlation ID for request tracing
     */
    correlationId(correlationId) {
        if (!this.event.metadata)
            this.event.metadata = {};
        this.event.metadata.correlationId = correlationId;
        return this;
    }
    /**
     * 🔗 Set causation ID linking to the causing event
     */
    causationId(causationId) {
        if (!this.event.metadata)
            this.event.metadata = {};
        this.event.metadata.causationId = causationId;
        return this;
    }
    /**
     * 👤 Set the user who triggered this event
     */
    userId(userId) {
        if (!this.event.metadata)
            this.event.metadata = {};
        this.event.metadata.userId = userId;
        return this;
    }
    metadata(keyOrMetadata, value) {
        if (!this.event.metadata)
            this.event.metadata = {};
        if (typeof keyOrMetadata === 'string') {
            this.event.metadata[keyOrMetadata] = value;
        }
        else {
            this.event.metadata = { ...this.event.metadata, ...keyOrMetadata };
        }
        return this;
    }
    /**
     * ✨ Build the beautiful domain event
     */
    build() {
        // Generate event ID if not provided
        if (!this.event.eventId) {
            this.event.eventId = crypto.randomUUID();
        }
        // Validate required fields
        if (!this.event.eventType) {
            throw new Error('🚫 Event type is required');
        }
        if (!this.event.aggregateId) {
            throw new Error('🚫 Aggregate ID is required');
        }
        if (!this.event.aggregateType) {
            throw new Error('🚫 Aggregate type is required');
        }
        if (!this.event.source) {
            throw new Error('🚫 Source is required');
        }
        if (this.event.data === undefined) {
            throw new Error('🚫 Event data is required');
        }
        return this.event;
    }
}
// ============================================================================
// Convenience Functions
// ============================================================================
/**
 * 🏗️ Create a new event builder
 */
export function createEvent() {
    return new EventBuilder();
}
/**
 * 🎯 Create an event for a specific aggregate
 */
export function createEventFor(aggregateType, aggregateId) {
    return new EventBuilder().aggregate(aggregateId, aggregateType);
}
/**
 * 📝 Create a domain event with common patterns
 */
export function createDomainEvent(options) {
    const builder = createEvent()
        .type(options.eventType)
        .aggregate(options.aggregateId, options.aggregateType)
        .data(options.data)
        .source(options.source);
    if (options.correlationId) {
        builder.correlationId(options.correlationId);
    }
    if (options.userId) {
        builder.userId(options.userId);
    }
    if (options.metadata) {
        builder.metadata(options.metadata);
    }
    return builder.build();
}
// ============================================================================
// Event Type Helpers
// ============================================================================
/**
 * 🏷️ Create event type with consistent naming
 */
export function eventType(aggregate, action) {
    return `${aggregate.toLowerCase()}.${action.toLowerCase()}`;
}
/**
 * 📋 Common event types
 */
export const EventTypes = {
    // State changes
    activated: (aggregate) => eventType(aggregate, 'activated'),
    archived: (aggregate) => eventType(aggregate, 'archived'),
    cancelled: (aggregate) => eventType(aggregate, 'cancelled'),
    completed: (aggregate) => eventType(aggregate, 'completed'),
    confirmed: (aggregate) => eventType(aggregate, 'confirmed'),
    // CRUD operations
    created: (aggregate) => eventType(aggregate, 'created'),
    deactivated: (aggregate) => eventType(aggregate, 'deactivated'),
    deleted: (aggregate) => eventType(aggregate, 'deleted'),
    rescheduled: (aggregate) => eventType(aggregate, 'rescheduled'),
    // Appointment specific
    scheduled: (aggregate) => eventType(aggregate, 'scheduled'),
    // Sync operations
    synced: (aggregate) => eventType(aggregate, 'synced'),
    syncFailed: (aggregate) => eventType(aggregate, 'sync_failed'),
    updated: (aggregate) => eventType(aggregate, 'updated'),
};
// ============================================================================
// Subject Builders
// ============================================================================
/**
 * 🎯 Build NATS subject patterns
 */
export class SubjectBuilder {
    parts = [];
    /**
     * 🏢 Add aggregate type
     */
    aggregate(type) {
        this.parts.push(type.toLowerCase());
        return this;
    }
    /**
     * 📡 Add events namespace
     */
    events() {
        this.parts.push('events');
        return this;
    }
    /**
     * 🏷️ Add event action
     */
    action(action) {
        this.parts.push(action.toLowerCase());
        return this;
    }
    /**
     * 🆔 Add specific ID
     */
    id(id) {
        this.parts.push(id);
        return this;
    }
    /**
     * 🌟 Add wildcard
     */
    wildcard() {
        this.parts.push('*');
        return this;
    }
    /**
     * ✨ Build the subject
     */
    build() {
        return this.parts.join('.');
    }
}
/**
 * 🎯 Create a subject builder
 */
export function subject() {
    return new SubjectBuilder();
}
/**
 * 📋 Common subject patterns
 */
export const Subjects = {
    // Aggregate-specific events
    aggregateEvents: (aggregate, id) => subject().aggregate(aggregate).id(id).events().wildcard().build(),
    // All events for an aggregate
    allEvents: (aggregate) => subject().aggregate(aggregate).events().wildcard().build(),
    // Specific event type
    eventType: (aggregate, action) => subject().aggregate(aggregate).events().action(action).build(),
};
//# sourceMappingURL=EventBuilder.js.map