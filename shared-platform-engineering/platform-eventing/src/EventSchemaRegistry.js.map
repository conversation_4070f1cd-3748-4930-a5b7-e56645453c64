{"version": 3, "file": "EventSchemaRegistry.js", "sourceRoot": "", "sources": ["EventSchemaRegistry.ts"], "names": [], "mappings": "AAAA;;;;;GAKG;AAEH,OAAO,GAAG,MAAM,KAAK,CAAC;AACtB,OAAO,UAAU,MAAM,aAAa,CAAC;AAqCrC,+EAA+E;AAC/E,wBAAwB;AACxB,+EAA+E;AAE/E,MAAM,OAAO,mBAAmB;IACtB,OAAO,GAAG,IAAI,GAAG,EAAoC,CAAC;IACtD,GAAG,CAAM;IAEjB;QACE,IAAI,CAAC,GAAG,GAAG,IAAI,GAAG,CAAC;YACjB,SAAS,EAAE,IAAI;YACf,gBAAgB,EAAE,KAAK;YACvB,MAAM,EAAE,KAAK;YACb,OAAO,EAAE,IAAI;SACd,CAAC,CAAC;QACH,UAAU,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;IACvB,CAAC;IAED;;OAEG;IACH,cAAc,CAAC,MAAmB;QAChC,MAAM,EAAE,SAAS,EAAE,OAAO,EAAE,GAAG,MAAM,CAAC;QAEtC,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,SAAS,CAAC,EAAE,CAAC;YACjC,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,SAAS,EAAE,IAAI,GAAG,EAAE,CAAC,CAAC;QACzC,CAAC;QAED,MAAM,YAAY,GAAG,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,SAAS,CAAC,CAAC;QACjD,6DAA6D;QAE7D,6CAA6C;QAC7C,IAAI,YAAY,IAAI,YAAY,CAAC,IAAI,GAAG,CAAC,EAAE,CAAC;YAC1C,MAAM,aAAa,GAAG,IAAI,CAAC,GAAG,CAAC,GAAG,YAAY,CAAC,IAAI,EAAE,CAAC,CAAC;YACvD,MAAM,YAAY,GAAG,YAAY,CAAC,GAAG,CAAC,aAAa,CAAC,CAAC;YACrD,IAAI,CAAC,YAAY,EAAE,CAAC;gBAClB,sEAAsE;gBACtE,MAAM,IAAI,KAAK,CACb,oCAAoC,SAAS,KAAK,aAAa,GAAG,CACnE,CAAC;YACJ,CAAC;YAED,MAAM,aAAa,GAAG,IAAI,CAAC,kBAAkB,CAAC,YAAY,EAAE,MAAM,CAAC,CAAC;YACpE,IAAI,CAAC,aAAa,CAAC,UAAU,EAAE,CAAC;gBAC9B,MAAM,IAAI,KAAK,CACb,yCAAyC,SAAS,KAAK,OAAO,KAAK,aAAa,CAAC,MAAM,CAAC,IAAI,CAC1F,IAAI,CACL,EAAE,CACJ,CAAC;YACJ,CAAC;QACH,CAAC;QAED,YAAY,EAAE,GAAG,CAAC,OAAO,EAAE,MAAM,CAAC,CAAC;IACrC,CAAC;IAED;;OAEG;IACH,SAAS,CAAC,SAAiB,EAAE,OAAgB;QAC3C,MAAM,YAAY,GAAG,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,SAAS,CAAC,CAAC;QACjD,IAAI,CAAC,YAAY;YAAE,OAAO,IAAI,CAAC;QAE/B,IAAI,OAAO,KAAK,SAAS,EAAE,CAAC;YAC1B,OAAO,YAAY,CAAC,GAAG,CAAC,OAAO,CAAC,IAAI,IAAI,CAAC;QAC3C,CAAC;QAED,wBAAwB;QACxB,MAAM,aAAa,GAAG,IAAI,CAAC,GAAG,CAAC,GAAG,YAAY,CAAC,IAAI,EAAE,CAAC,CAAC;QACvD,OAAO,YAAY,CAAC,GAAG,CAAC,aAAa,CAAC,IAAI,IAAI,CAAC;IACjD,CAAC;IAED;;OAEG;IACH,iBAAiB,CAAC,SAAiB;QACjC,MAAM,YAAY,GAAG,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,SAAS,CAAC,CAAC;QACjD,OAAO,YAAY,CAAC,CAAC,CAAC,KAAK,CAAC,IAAI,CAAC,YAAY,CAAC,IAAI,EAAE,CAAC,CAAC,IAAI,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC;IACpE,CAAC;IAED;;OAEG;IACH,aAAa,CACX,SAAiB,EACjB,SAAkB,EAClB,OAAgB;QAEhB,MAAM,MAAM,GAAG,IAAI,CAAC,SAAS,CAAC,SAAS,EAAE,OAAO,CAAC,CAAC;QAElD,IAAI,CAAC,MAAM,EAAE,CAAC;YACZ,OAAO;gBACL,MAAM,EAAE;oBACN,mCAAmC,SAAS,GAC1C,OAAO,CAAC,CAAC,CAAC,KAAK,OAAO,EAAE,CAAC,CAAC,CAAC,EAC7B,EAAE;iBACH;gBACD,KAAK,EAAE,KAAK;gBACZ,OAAO,EAAE,OAAO,IAAI,CAAC;aACtB,CAAC;QACJ,CAAC;QAED,MAAM,QAAQ,GAAG,IAAI,CAAC,GAAG,CAAC,OAAO,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC;QACjD,MAAM,KAAK,GAAG,QAAQ,CAAC,SAAS,CAAC,CAAC;QAElC,MAAM,MAAM,GAA2B;YACrC,KAAK;YACL,OAAO,EAAE,MAAM,CAAC,OAAO;SACxB,CAAC;QAEF,IAAI,CAAC,KAAK,IAAI,QAAQ,CAAC,MAAM,EAAE,CAAC;YAC9B,MAAM,CAAC,MAAM,GAAG,QAAQ,CAAC,MAAM,CAAC,GAAG,CACjC,CAAC,KAAK,EAAE,EAAE,CAAC,GAAG,KAAK,CAAC,YAAY,IAAI,MAAM,KAAK,KAAK,CAAC,OAAO,EAAE,CAC/D,CAAC;QACJ,CAAC;QAED,2BAA2B;QAC3B,IAAI,MAAM,CAAC,UAAU,EAAE,CAAC;YACtB,MAAM,CAAC,QAAQ,GAAG;gBAChB,UAAU,SAAS,KAAK,MAAM,CAAC,OAAO,iBACpC,MAAM,CAAC,kBAAkB,CAAC,CAAC,CAAC,KAAK,MAAM,CAAC,kBAAkB,EAAE,CAAC,CAAC,CAAC,EACjE,EAAE;aACH,CAAC;QACJ,CAAC;QAED,OAAO,MAAM,CAAC;IAChB,CAAC;IAED;;OAEG;IACH,kBAAkB,CAChB,SAAsB,EACtB,SAAsB;QAEtB,MAAM,MAAM,GAA8B;YACxC,eAAe,EAAE,EAAE;YACnB,UAAU,EAAE,IAAI;YAChB,MAAM,EAAE,EAAE;YACV,QAAQ,EAAE,EAAE;SACb,CAAC;QAEF,8BAA8B;QAC9B,IAAI,SAAS,CAAC,OAAO,IAAI,SAAS,CAAC,OAAO,EAAE,CAAC;YAC3C,MAAM,CAAC,UAAU,GAAG,KAAK,CAAC;YAC1B,MAAM,CAAC,MAAM,CAAC,IAAI,CAChB,eAAe,SAAS,CAAC,OAAO,yBAAyB,SAAS,CAAC,OAAO,EAAE,CAC7E,CAAC;QACJ,CAAC;QAED,wCAAwC;QACxC,QAAQ,SAAS,CAAC,iBAAiB,EAAE,CAAC;YACpC,KAAK,UAAU;gBACb,IAAI,CAAC,0BAA0B,CAAC,SAAS,EAAE,SAAS,EAAE,MAAM,CAAC,CAAC;gBAC9D,MAAM;YACR,KAAK,SAAS;gBACZ,IAAI,CAAC,yBAAyB,CAAC,SAAS,EAAE,SAAS,EAAE,MAAM,CAAC,CAAC;gBAC7D,MAAM;YACR,KAAK,MAAM;gBACT,IAAI,CAAC,0BAA0B,CAAC,SAAS,EAAE,SAAS,EAAE,MAAM,CAAC,CAAC;gBAC9D,IAAI,CAAC,yBAAyB,CAAC,SAAS,EAAE,SAAS,EAAE,MAAM,CAAC,CAAC;gBAC7D,MAAM;YACR,KAAK,MAAM;gBACT,MAAM,CAAC,QAAQ,CAAC,IAAI,CAClB,wDAAwD,CACzD,CAAC;gBACF,MAAM;QACV,CAAC;QAED,OAAO,MAAM,CAAC;IAChB,CAAC;IAED;;OAEG;IACH,aAAa;QACX,OAAO,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,IAAI,EAAE,CAAC,CAAC;IACzC,CAAC;IAED;;OAEG;IACH,YAAY,CAAC,SAAiB;QAC5B,OAAO,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,SAAS,CAAC,CAAC;IACrC,CAAC;IAED;;OAEG;IACH,wBAAwB,CACtB,SAAiB,EACjB,iBAAyB;QAEzB,MAAM,YAAY,GAAG,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,SAAS,CAAC,CAAC;QACjD,IAAI,CAAC,YAAY;YAAE,OAAO,CAAC,CAAC;QAE5B,IAAI,YAAY,GAAG,CAAC,CAAC;QACrB,KAAK,MAAM,CAAC,OAAO,EAAE,MAAM,CAAC,IAAI,YAAY,CAAC,OAAO,EAAE,EAAE,CAAC;YACvD,IAAI,OAAO,IAAI,iBAAiB,IAAI,MAAM,CAAC,UAAU,EAAE,CAAC;gBACtD,YAAY,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC;gBAC7B,YAAY,EAAE,CAAC;YACjB,CAAC;QACH,CAAC;QAED,OAAO,YAAY,CAAC;IACtB,CAAC;IAED,+EAA+E;IAC/E,kBAAkB;IAClB,+EAA+E;IAEvE,0BAA0B,CAChC,SAAsB,EACtB,SAAsB,EACtB,MAAiC;QAEjC,wFAAwF;QACxF,MAAM,QAAQ,GACX,SAAS,CAAC,MAAM,CAAC,UAAsC,IAAI,EAAE,CAAC;QACjE,MAAM,QAAQ,GACX,SAAS,CAAC,MAAM,CAAC,UAAsC,IAAI,EAAE,CAAC;QACjE,MAAM,WAAW,GAAI,SAAS,CAAC,MAAM,CAAC,QAAqB,IAAI,EAAE,CAAC;QAClE,MAAM,WAAW,GAAI,SAAS,CAAC,MAAM,CAAC,QAAqB,IAAI,EAAE,CAAC;QAElE,oCAAoC;QACpC,KAAK,MAAM,KAAK,IAAI,WAAW,EAAE,CAAC;YAChC,IAAI,CAAC,WAAW,CAAC,QAAQ,CAAC,KAAK,CAAC,EAAE,CAAC;gBACjC,MAAM,CAAC,UAAU,GAAG,KAAK,CAAC;gBAC1B,MAAM,CAAC,eAAe,CAAC,IAAI,CAAC,mBAAmB,KAAK,eAAe,CAAC,CAAC;YACvE,CAAC;QACH,CAAC;QAED,2BAA2B;QAC3B,KAAK,MAAM,KAAK,IAAI,MAAM,CAAC,IAAI,CAAC,QAAQ,CAAC,EAAE,CAAC;YAC1C,IAAI,CAAC,CAAC,KAAK,IAAI,QAAQ,CAAC,EAAE,CAAC;gBACzB,MAAM,CAAC,UAAU,GAAG,KAAK,CAAC;gBAC1B,MAAM,CAAC,eAAe,CAAC,IAAI,CAAC,UAAU,KAAK,eAAe,CAAC,CAAC;YAC9D,CAAC;QACH,CAAC;QAED,yBAAyB;QACzB,KAAK,MAAM,KAAK,IAAI,MAAM,CAAC,IAAI,CAAC,QAAQ,CAAC,EAAE,CAAC;YAC1C,IAAI,KAAK,IAAI,QAAQ,EAAE,CAAC;gBACtB,MAAM,OAAO,GAAI,QAAQ,CAAC,KAAK,CAA6B,EAAE,IAAI,CAAC;gBACnE,MAAM,OAAO,GAAI,QAAQ,CAAC,KAAK,CAA6B,EAAE,IAAI,CAAC;gBACnE,IAAI,OAAO,IAAI,OAAO,IAAI,OAAO,KAAK,OAAO,EAAE,CAAC;oBAC9C,MAAM,CAAC,UAAU,GAAG,KAAK,CAAC;oBAC1B,MAAM,CAAC,eAAe,CAAC,IAAI,CACzB,UAAU,KAAK,uBAAuB,OAAO,OAAO,OAAO,EAAE,CAC9D,CAAC;gBACJ,CAAC;YACH,CAAC;QACH,CAAC;IACH,CAAC;IAEO,yBAAyB,CAC/B,SAAsB,EACtB,SAAsB,EACtB,MAAiC;QAEjC,MAAM,WAAW,GAAI,SAAS,CAAC,MAAM,CAAC,QAAqB,IAAI,EAAE,CAAC;QAClE,MAAM,WAAW,GAAI,SAAS,CAAC,MAAM,CAAC,QAAqB,IAAI,EAAE,CAAC;QAElE,gCAAgC;QAChC,KAAK,MAAM,KAAK,IAAI,WAAW,EAAE,CAAC;YAChC,IAAI,CAAC,WAAW,CAAC,QAAQ,CAAC,KAAK,CAAC,EAAE,CAAC;gBACjC,MAAM,CAAC,UAAU,GAAG,KAAK,CAAC;gBAC1B,MAAM,CAAC,eAAe,CAAC,IAAI,CAAC,uBAAuB,KAAK,SAAS,CAAC,CAAC;YACrE,CAAC;QACH,CAAC;IACH,CAAC;CACF;AAED,+EAA+E;AAC/E,2BAA2B;AAC3B,+EAA+E;AAE/E,MAAM,CAAC,MAAM,yBAAyB,GAAG,IAAI,mBAAmB,EAAE,CAAC"}