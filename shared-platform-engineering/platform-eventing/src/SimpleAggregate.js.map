{"version": 3, "file": "SimpleAggregate.js", "sourceRoot": "", "sources": ["SimpleAggregate.ts"], "names": [], "mappings": "AAAA;;;;GAIG;AAEH,OAAO,EAAE,WAAW,EAAE,UAAU,EAAE,MAAM,gBAAgB,CAAC;AAUzD;;;;;GAKG;AACH,MAAM,OAAgB,aAAa;IAGjB,EAAE,CAAS;IACpB,KAAK,GAAkB,IAAI,CAAC;IACzB,OAAO,GAAG,CAAC,CAAC;IACZ,aAAa,CAAiB;IAExC,YACE,EAAU,EACV,YAAqB,EACrB,aAA6B;QAE7B,IAAI,CAAC,EAAE,GAAG,EAAE,CAAC;QACb,IAAI,CAAC,KAAK,GAAG,YAAY,IAAI,IAAI,CAAC;QAClC,IAAI,CAAC,aAAa,GAAG,aAAa,CAAC;IACrC,CAAC;IAWD;;OAEG;IACI,wBAAwB,CAAC,OAAgB;QAC9C,IAAI,CAAC;YACH,MAAM,MAAM,GAAG,IAAI,CAAC,cAAc,CAAC,OAAO,CAAC,CAAC;YAC5C,IAAI,CAAC,OAAO,IAAI,MAAM,CAAC,MAAM,CAAC;YAE9B,OAAO;gBACL,WAAW,EAAE,IAAI,CAAC,EAAE;gBACpB,MAAM,EAAE,MAAM,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,OAAO,CAAC;gBACpC,OAAO,EAAE,IAAI;gBACb,OAAO,EAAE,IAAI,CAAC,OAAO;aACtB,CAAC;QACJ,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,OAAO;gBACL,WAAW,EAAE,IAAI,CAAC,EAAE;gBACpB,KAAK,EAAE,KAAK,YAAY,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,eAAe;gBAC/D,OAAO,EAAE,KAAK;gBACd,OAAO,EAAE,IAAI,CAAC,OAAO;aACtB,CAAC;QACJ,CAAC;IACH,CAAC;IAED;;;;;OAKG;IACI,KAAK,CAAC,wBAAwB,CACnC,OAAgB,EAChB,WAAqB;QAErB,IAAI,CAAC;YACH,MAAM,MAAM,GAAG,IAAI,CAAC,cAAc,CAAC,OAAO,CAAC,CAAC;YAC5C,IAAI,CAAC,OAAO,IAAI,MAAM,CAAC,MAAM,CAAC;YAE9B,iDAAiD;YACjD,IAAI,IAAI,CAAC,aAAa,IAAI,MAAM,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;gBAC5C,MAAM,IAAI,CAAC,aAAa,CAAC,WAAW,CAAC,MAAM,EAAE,WAAW,CAAC,CAAC;YAC5D,CAAC;YAED,OAAO;gBACL,WAAW,EAAE,IAAI,CAAC,EAAE;gBACpB,MAAM,EAAE,MAAM,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,OAAO,CAAC;gBACpC,OAAO,EAAE,IAAI;gBACb,OAAO,EAAE,IAAI,CAAC,OAAO;aACtB,CAAC;QACJ,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,OAAO;gBACL,WAAW,EAAE,IAAI,CAAC,EAAE;gBACpB,KAAK,EAAE,KAAK,YAAY,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,eAAe;gBAC/D,OAAO,EAAE,KAAK;gBACd,OAAO,EAAE,IAAI,CAAC,OAAO;aACtB,CAAC;QACJ,CAAC;IACH,CAAC;IAED,+EAA+E;IAC/E,yBAAyB;IACzB,+EAA+E;IAE/E;;OAEG;IACO,WAAW,CACnB,SAAiB,EACjB,IAAW,EACX,OAKC;QAED,MAAM,OAAO,GAAG,WAAW,EAAS;aACjC,IAAI,CAAC,SAAS,CAAC;aACf,SAAS,CAAC,IAAI,CAAC,EAAE,EAAE,IAAI,CAAC,gBAAgB,EAAE,CAAC;aAC3C,IAAI,CAAC,IAAI,CAAC;aACV,MAAM,CAAC,IAAI,CAAC,SAAS,EAAE,CAAC;aACxB,OAAO,CAAC,IAAI,CAAC,OAAO,GAAG,CAAC,CAAC,CAAC;QAE7B,yCAAyC;QACzC,IAAI,OAAO,EAAE,aAAa,EAAE,CAAC;YAC3B,OAAO,CAAC,aAAa,CAAC,OAAO,CAAC,aAAa,CAAC,CAAC;QAC/C,CAAC;QACD,IAAI,OAAO,EAAE,WAAW,EAAE,CAAC;YACzB,OAAO,CAAC,WAAW,CAAC,OAAO,CAAC,WAAW,CAAC,CAAC;QAC3C,CAAC;QACD,IAAI,OAAO,EAAE,MAAM,EAAE,CAAC;YACpB,OAAO,CAAC,MAAM,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC;QACjC,CAAC;QACD,IAAI,OAAO,EAAE,QAAQ,EAAE,CAAC;YACtB,OAAO,CAAC,QAAQ,CAAC,OAAO,CAAC,QAAQ,CAAC,CAAC;QACrC,CAAC;QAED,OAAO,OAAO,CAAC,KAAK,EAAE,CAAC;IACzB,CAAC;IAED;;OAEG;IACO,sBAAsB,CAC9B,SAAiB,EACjB,IAAW,EACX,OAAgB;QAEhB,OAAO,IAAI,CAAC,WAAW,CAAC,SAAS,EAAE,IAAI,EAAE;YACvC,aAAa,EAAE,OAAO,CAAC,QAAQ,CAAC,aAAa;YAC7C,QAAQ,EAAE;gBACR,WAAW,EAAE,OAAO,CAAC,WAAW;gBAChC,GAAG,OAAO,CAAC,QAAQ;aACpB;YACD,MAAM,EAAE,OAAO,CAAC,QAAQ,CAAC,MAAM;SAChC,CAAC,CAAC;IACL,CAAC;IAED;;OAEG;IACO,gBAAgB,CACxB,MAA+B,EAC/B,IAAW,EACX,OAKC;QAED,MAAM,SAAS,GAAG,UAAU,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,gBAAgB,EAAE,CAAC,CAAC;QAC9D,OAAO,IAAI,CAAC,WAAW,CAAC,SAAS,EAAE,IAAI,EAAE,OAAO,CAAC,CAAC;IACpD,CAAC;IAgBD,+EAA+E;IAC/E,mBAAmB;IACnB,+EAA+E;IAE/E;;OAEG;IACI,UAAU;QACf,OAAO,IAAI,CAAC,OAAO,CAAC;IACtB,CAAC;IAED;;OAEG;IACO,gBAAgB,CACxB,YAA8C;QAE9C,IAAI,CAAC,KAAK,GAAG,YAAY,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;IACxC,CAAC;IAED;;OAEG;IACI,cAAc,CAAC,OAAe;QACnC,IAAI,CAAC,OAAO,GAAG,OAAO,CAAC;IACzB,CAAC;IAED;;OAEG;IACI,gBAAgB,CAAC,aAA4B;QAClD,IAAI,CAAC,aAAa,GAAG,aAAa,CAAC;IACrC,CAAC;IAED,+EAA+E;IAC/E,qBAAqB;IACrB,+EAA+E;IAE/E;;OAEG;IACO,eAAe,CAAC,OAAgB;QACxC,IAAI,CAAC,OAAO,CAAC,WAAW,EAAE,CAAC;YACzB,MAAM,IAAI,KAAK,CAAC,0BAA0B,CAAC,CAAC;QAC9C,CAAC;QACD,IAAI,CAAC,OAAO,CAAC,QAAQ,EAAE,aAAa,EAAE,CAAC;YACrC,MAAM,IAAI,KAAK,CAAC,gDAAgD,CAAC,CAAC;QACpE,CAAC;IACH,CAAC;IAED;;OAEG;IACO,gBAAgB,CAAC,OAAe,EAAE,IAAa;QACvD,MAAM,KAAK,GAAG,IAAI,KAAK,CAAC,OAAO,CAA8B,CAAC;QAC9D,IAAI,IAAI,EAAE,CAAC;YACT,KAAK,CAAC,IAAI,GAAG,IAAI,CAAC;QACpB,CAAC;QACD,MAAM,KAAK,CAAC;IACd,CAAC;CACF;AAqBD,+EAA+E;AAC/E,oBAAoB;AACpB,+EAA+E;AAE/E;;GAEG;AACH,MAAM,CAAC,MAAM,gBAAgB,GAAG;IAC9B;;OAEG;IACH,MAAM,CACJ,cAAuC,EACvC,EAAU,EACV,YAAgC,EAChC,aAA6B;QAE7B,OAAO,IAAI,cAAc,CAAC,EAAE,EAAE,YAAY,EAAE,aAAa,CAAC,CAAC;IAC7D,CAAC;IAED;;OAEG;IACH,OAAO,CACL,cAAuC,EACvC,EAAU,EACV,KAAwB,EACxB,OAAO,GAAG,CAAC,EACX,aAA6B;QAE7B,MAAM,SAAS,GAAG,IAAI,cAAc,CAAC,EAAE,EAAE,KAAK,EAAE,aAAa,CAAC,CAAC;QAC/D,2CAA2C;QAC3C,SAAS,CAAC,cAAc,CAAC,OAAO,CAAC,CAAC;QAClC,OAAO,SAAS,CAAC;IACnB,CAAC;CACF,CAAC;AAEF;;GAEG;AACH,MAAM,UAAU,eAAe,CAC7B,cAAuC,EACvC,EAAU,EACV,YAAgC,EAChC,aAA6B;IAE7B,OAAO,gBAAgB,CAAC,MAAM,CAC5B,cAAc,EACd,EAAE,EACF,YAAY,EACZ,aAAa,CACd,CAAC;AACJ,CAAC"}