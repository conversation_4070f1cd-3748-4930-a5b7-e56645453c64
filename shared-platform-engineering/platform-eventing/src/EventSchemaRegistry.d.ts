/**
 * 📋 Event Schema Registry
 *
 * Provides event schema versioning, validation, and compatibility checking
 * for reliable event-driven architecture with backward/forward compatibility.
 */
export interface EventSchema {
    eventType: string;
    version: number;
    schema: Record<string, unknown>;
    compatibilityMode: CompatibilityMode;
    description?: string;
    examples?: unknown[];
    deprecated?: boolean;
    deprecationMessage?: string;
}
export type CompatibilityMode = 'backward' | 'forward' | 'full' | 'none';
export interface SchemaValidationResult {
    valid: boolean;
    errors?: string[];
    warnings?: string[];
    version: number;
}
export interface SchemaCompatibilityResult {
    compatible: boolean;
    issues: string[];
    breakingChanges: string[];
    warnings: string[];
}
export declare class EventSchemaRegistry {
    private schemas;
    private ajv;
    constructor();
    /**
     * Register a new event schema version
     */
    registerSchema(schema: EventSchema): void;
    /**
     * Get schema for specific event type and version
     */
    getSchema(eventType: string, version?: number): EventSchema | null;
    /**
     * Get all versions for an event type
     */
    getSchemaVersions(eventType: string): number[];
    /**
     * Validate event data against schema
     */
    validateEvent(eventType: string, eventData: unknown, version?: number): SchemaValidationResult;
    /**
     * Check compatibility between two schema versions
     */
    checkCompatibility(oldSchema: EventSchema, newSchema: EventSchema): SchemaCompatibilityResult;
    /**
     * Get all registered event types
     */
    getEventTypes(): string[];
    /**
     * Check if event type exists
     */
    hasEventType(eventType: string): boolean;
    /**
     * Remove deprecated schemas older than specified version
     */
    cleanupDeprecatedSchemas(eventType: string, keepVersionsAfter: number): number;
    private checkBackwardCompatibility;
    private checkForwardCompatibility;
}
export declare const globalEventSchemaRegistry: EventSchemaRegistry;
//# sourceMappingURL=EventSchemaRegistry.d.ts.map