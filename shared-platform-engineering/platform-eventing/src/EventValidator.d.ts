/**
 * 🔍 Event Validator
 *
 * Provides event validation utilities that integrate with the schema registry
 * and domain event system for reliable event processing.
 */
import { type EventSchemaRegistry } from './EventSchemaRegistry';
import type { DomainEvent } from './types';
export interface ValidationConfig {
    strictMode: boolean;
    allowUnknownEvents: boolean;
    defaultVersion?: number;
    logValidationErrors: boolean;
}
export declare const DEFAULT_VALIDATION_CONFIG: ValidationConfig;
export interface EventValidationResult {
    valid: boolean;
    eventType: string;
    version: number;
    errors: string[];
    warnings: string[];
    metadata: {
        schemaFound: boolean;
        validationTime: number;
        timestamp: string;
    };
}
export interface BatchValidationResult {
    totalEvents: number;
    validEvents: number;
    invalidEvents: number;
    results: EventValidationResult[];
    summary: {
        validationTime: number;
        errorsByType: Record<string, number>;
        warningsByType: Record<string, number>;
    };
}
export declare class EventValidator {
    private schemaRegistry;
    private config;
    constructor(schemaRegistry?: EventSchemaRegistry, config?: ValidationConfig);
    /**
     * Validate a single domain event
     */
    validateEvent(event: DomainEvent): EventValidationResult;
    /**
     * Validate multiple events in batch
     */
    validateEvents(events: DomainEvent[]): BatchValidationResult;
    /**
     * Validate event before publishing to outbox
     */
    validateForOutbox(event: DomainEvent): void;
    /**
     * Get validation statistics
     */
    getValidationStats(results: EventValidationResult[]): {
        successRate: number;
        averageValidationTime: number;
        mostCommonErrors: Array<{
            error: string;
            count: number;
        }>;
        eventTypeBreakdown: Record<string, {
            valid: number;
            invalid: number;
        }>;
    };
    private validateEventStructure;
    private categorizeError;
    private categorizeWarning;
}
export declare const globalEventValidator: EventValidator;
//# sourceMappingURL=EventValidator.d.ts.map