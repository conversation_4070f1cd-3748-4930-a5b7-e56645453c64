#!/bin/bash

# Advanced CLI Setup Script for macOS with zsh
echo "🚀 Setting up advanced colorful CLI environment..."

# Install Homebrew if not installed
if ! command -v brew &> /dev/null; then
    echo "Installing Homebrew..."
    /bin/bash -c "$(curl -fsSL https://raw.githubusercontent.com/Homebrew/install/HEAD/install.sh)"
fi

# Install essential tools
echo "📦 Installing CLI tools..."
brew install starship fzf eza zoxide bat fd ripgrep

# Install Oh My Zsh if not installed
if [ ! -d "$HOME/.oh-my-zsh" ]; then
    echo "Installing Oh My Zsh..."
    sh -c "$(curl -fsSL https://raw.github.com/ohmyzsh/ohmyzsh/master/tools/install.sh)" "" --unattended
fi

# Install Powerlevel10k theme
echo "Installing Powerlevel10k theme..."
git clone --depth=1 https://github.com/romkatv/powerlevel10k.git ${ZSH_CUSTOM:-$HOME/.oh-my-zsh/custom}/themes/powerlevel10k

# Install zsh plugins
echo "Installing zsh plugins..."
git clone https://github.com/zsh-users/zsh-autosuggestions ${ZSH_CUSTOM:-~/.oh-my-zsh/custom}/plugins/zsh-autosuggestions
git clone https://github.com/zsh-users/zsh-syntax-highlighting.git ${ZSH_CUSTOM:-~/.oh-my-zsh/custom}/plugins/zsh-syntax-highlighting

# Setup fzf
echo "Setting up fzf..."
$(brew --prefix)/opt/fzf/install --all

# Backup existing .zshrc
if [ -f "$HOME/.zshrc" ]; then
    cp "$HOME/.zshrc" "$HOME/.zshrc.backup.$(date +%Y%m%d_%H%M%S)"
fi

# Create enhanced .zshrc
cat > "$HOME/.zshrc" << 'EOF'
# Oh My Zsh configuration
export ZSH="$HOME/.oh-my-zsh"
ZSH_THEME="powerlevel10k/powerlevel10k"

# Plugins
plugins=(
    git
    zsh-autosuggestions
    zsh-syntax-highlighting
    docker
    node
    npm
    yarn
    vscode
)

source $ZSH/oh-my-zsh.sh

# Starship prompt (alternative to Powerlevel10k)
# eval "$(starship init zsh)"

# Modern CLI tools
eval "$(zoxide init zsh)"

# Aliases for modern tools
alias ls='eza --color=always --group-directories-first'
alias ll='eza -la --color=always --group-directories-first'
alias la='eza -a --color=always --group-directories-first'
alias lt='eza -aT --color=always --group-directories-first'
alias cat='bat --paging=never'
alias find='fd'
alias cd='z'

# fzf configuration
export FZF_DEFAULT_COMMAND='fd --type f --hidden --follow --exclude .git'
export FZF_DEFAULT_OPTS='--height 40% --layout=reverse --border --preview "bat --color=always --style=header,grid --line-range :300 {}"'

# Custom functions
# Preview files with fzf
fzf-preview() {
    fzf --preview 'bat --color=always --style=header,grid --line-range :300 {}'
}

# Git log with fzf
fzf-git-log() {
    git log --oneline | fzf --preview 'git show --color=always {1}'
}

# Directory navigation with fzf
fzf-cd() {
    local dir
    dir=$(fd --type d | fzf --preview 'eza -la --color=always {}') && cd "$dir"
}

# Bind Alt+C to fzf-cd
bindkey '\ec' fzf-cd

# Enhanced history search
autoload -U up-line-or-beginning-search
autoload -U down-line-or-beginning-search
zle -N up-line-or-beginning-search
zle -N down-line-or-beginning-search
bindkey "^[[A" up-line-or-beginning-search
bindkey "^[[B" down-line-or-beginning-search

# Auto-suggestions configuration
ZSH_AUTOSUGGEST_HIGHLIGHT_STYLE="fg=#666666"
ZSH_AUTOSUGGEST_STRATEGY=(history completion)

# Syntax highlighting configuration
ZSH_HIGHLIGHT_HIGHLIGHTERS=(main brackets pattern)

# Load custom configurations
[ -f ~/.zshrc.local ] && source ~/.zshrc.local

EOF

echo "✅ Setup complete!"
echo ""
echo "🎨 Next steps:"
echo "1. Restart your terminal or run: source ~/.zshrc"
echo "2. Run 'p10k configure' to setup Powerlevel10k theme"
echo "3. Install a Nerd Font for better icons: https://www.nerdfonts.com/"
echo ""
echo "🔧 Key features enabled:"
echo "- Colorful autocomplete and syntax highlighting"
echo "- Smart directory navigation with zoxide (use 'z' instead of 'cd')"
echo "- Modern file listing with eza"
echo "- Fuzzy finding with fzf (Ctrl+R for history, Ctrl+T for files)"
echo "- Syntax-highlighted file preview with bat"
echo "- Git integration and shortcuts"
echo ""
echo "💡 Try these commands:"
echo "- 'll' for detailed file listing"
echo "- 'z projectname' for smart cd"
echo "- 'Ctrl+R' for fuzzy history search"
echo "- 'fzf-preview' for file selection with preview" 