/**
 * 🎯 APPOINTMENT ENTITY - Domain-Driven Design Entity
 *
 * Following DDD patterns with proper business logic encapsulation
 */

import 'reflect-metadata';
import {
  IsString,
  IsEmail,
  IsOptional,
  IsEnum,
  IsDate,
  IsNumber,
  IsPositive,
  IsInt,
  MinLength,
  IsUrl,
} from 'class-validator';
import { Type } from 'class-transformer';
import {
  BaseEntity,
  type UniqueEntityID,
  type IBaseEntityProps,
} from '@beauty-crm/product-domain-types';
import type {
  AppointmentStatus,
  AppointmentSource,
  Appointment,
} from '../schema/appointment-schema';

// ============================================================================
// 🎯 APPOINTMENT PROPS INTERFACE
// ============================================================================

export interface IAppointmentProps extends IBaseEntityProps {
  // Core identifiers
  salonId: string;
  customerId: string;
  staffId?: string;
  treatmentId: string;

  // Customer Info (denormalized for performance)
  customerName: string;
  customerEmail: string;
  customerPhone?: string;

  // Treatment Info (denormalized for performance)
  treatmentName: string;
  treatmentDuration: number;
  treatmentPrice: number;

  // Salon Info (denormalized for performance)
  salonName: string;
  salonLogo?: string;
  salonColor?: string;

  // Scheduling
  startTime: Date;
  endTime: Date;

  // Status & Metadata
  status: AppointmentStatus;
  notes?: string;
  locale: string;

  // Source tracking
  source: AppointmentSource;
  plannerAppointmentId?: string;

  // Timestamps (optional in props)
  confirmedAt?: Date;
  completedAt?: Date;
  cancelledAt?: Date;
}

// ============================================================================
// 🔥 APPOINTMENT ENTITY
// ============================================================================

export class AppointmentEntity extends BaseEntity<IAppointmentProps> {
  constructor(props: IAppointmentProps, id?: UniqueEntityID) {
    super(props, id);
  }

  // ============================================================================
  // 🎯 GETTERS
  // ============================================================================

  get salonId(): string {
    return this.props.salonId;
  }

  get customerId(): string {
    return this.props.customerId;
  }

  get staffId(): string | undefined {
    return this.props.staffId;
  }

  get treatmentId(): string {
    return this.props.treatmentId;
  }

  get customerName(): string {
    return this.props.customerName;
  }

  get customerEmail(): string {
    return this.props.customerEmail;
  }

  get customerPhone(): string | undefined {
    return this.props.customerPhone;
  }

  get treatmentName(): string {
    return this.props.treatmentName;
  }

  get treatmentDuration(): number {
    return this.props.treatmentDuration;
  }

  get treatmentPrice(): number {
    return this.props.treatmentPrice;
  }

  get salonName(): string {
    return this.props.salonName;
  }

  get salonLogo(): string | undefined {
    return this.props.salonLogo;
  }

  get salonColor(): string | undefined {
    return this.props.salonColor;
  }

  get startTime(): Date {
    return this.props.startTime;
  }

  get endTime(): Date {
    return this.props.endTime;
  }

  get status(): AppointmentStatus {
    return this.props.status;
  }

  get notes(): string | undefined {
    return this.props.notes;
  }

  get locale(): string {
    return this.props.locale;
  }

  get source(): AppointmentSource {
    return this.props.source;
  }

  get plannerAppointmentId(): string | undefined {
    return this.props.plannerAppointmentId;
  }

  get confirmedAt(): Date | undefined {
    return this.props.confirmedAt;
  }

  get completedAt(): Date | undefined {
    return this.props.completedAt;
  }

  get cancelledAt(): Date | undefined {
    return this.props.cancelledAt;
  }

  // ============================================================================
  // 🎯 BUSINESS LOGIC METHODS
  // ============================================================================

  /**
   * Confirms the appointment
   */
  public confirm(): void {
    if (this.props.status === 'CANCELLED') {
      throw new Error('Cannot confirm a cancelled appointment');
    }
    this.props.status = 'CONFIRMED';
    this.props.confirmedAt = new Date();
    this.touch();
  }

  /**
   * Cancels the appointment
   */
  public cancel(reason?: string): void {
    if (this.props.status === 'COMPLETED') {
      throw new Error('Cannot cancel a completed appointment');
    }
    this.props.status = 'CANCELLED';
    this.props.cancelledAt = new Date();
    if (reason) {
      this.props.notes = this.props.notes
        ? `${this.props.notes}\nCancellation reason: ${reason}`
        : `Cancellation reason: ${reason}`;
    }
    this.touch();
  }

  /**
   * Completes the appointment
   */
  public complete(): void {
    if (
      this.props.status !== 'IN_PROGRESS' &&
      this.props.status !== 'CONFIRMED'
    ) {
      throw new Error(
        'Can only complete appointments that are in progress or confirmed',
      );
    }
    this.props.status = 'COMPLETED';
    this.props.completedAt = new Date();
    this.touch();
  }

  /**
   * Reschedules the appointment
   */
  public reschedule(newStartTime: Date, newEndTime: Date): void {
    if (!this.canBeRescheduled()) {
      throw new Error('Appointment cannot be rescheduled in its current state');
    }
    this.props.startTime = newStartTime;
    this.props.endTime = newEndTime;
    this.props.status = 'RESCHEDULED';
    this.touch();
  }

  /**
   * Marks appointment as no-show
   */
  public markNoShow(): void {
    if (this.props.status === 'COMPLETED') {
      throw new Error('Cannot mark completed appointment as no-show');
    }
    this.props.status = 'NO_SHOW';
    this.touch();
  }

  /**
   * Starts the appointment (sets to in progress)
   */
  public start(): void {
    if (this.props.status !== 'CONFIRMED') {
      throw new Error('Can only start confirmed appointments');
    }
    this.props.status = 'IN_PROGRESS';
    this.touch();
  }

  /**
   * Updates customer information
   */
  public updateCustomerInfo(
    customerName?: string,
    customerEmail?: string,
    customerPhone?: string,
  ): void {
    if (customerName) this.props.customerName = customerName;
    if (customerEmail) this.props.customerEmail = customerEmail;
    if (customerPhone !== undefined) this.props.customerPhone = customerPhone;
    this.touch();
  }

  /**
   * Updates notes
   */
  public updateNotes(notes: string): void {
    this.props.notes = notes;
    this.touch();
  }

  // ============================================================================
  // 🎯 BUSINESS RULES & VALIDATION
  // ============================================================================

  /**
   * Checks if appointment can be rescheduled
   */
  public canBeRescheduled(): boolean {
    return !['COMPLETED', 'CANCELLED', 'NO_SHOW'].includes(this.props.status);
  }

  /**
   * Checks if appointment is in the past
   */
  public isPast(): boolean {
    return this.props.endTime < new Date();
  }

  /**
   * Converts to plain object for serialization
   */
  public toPlainObject(): Appointment {
    return {
      id: this.id.toString(),
      salonId: this.props.salonId,
      customerId: this.props.customerId,
      staffId: this.props.staffId,
      treatmentId: this.props.treatmentId,
      customerName: this.props.customerName,
      customerEmail: this.props.customerEmail,
      customerPhone: this.props.customerPhone,
      treatmentName: this.props.treatmentName,
      treatmentDuration: this.props.treatmentDuration,
      treatmentPrice: this.props.treatmentPrice,
      salonName: this.props.salonName,
      salonLogo: this.props.salonLogo,
      salonColor: this.props.salonColor,
      startTime: this.props.startTime,
      endTime: this.props.endTime,
      status: this.props.status,
      notes: this.props.notes,
      locale: this.props.locale,
      source: this.props.source,
      plannerAppointmentId: this.props.plannerAppointmentId,
      createdAt: this.props.createdAt,
      updatedAt: this.props.updatedAt,
      confirmedAt: this.props.confirmedAt,
      completedAt: this.props.completedAt,
      cancelledAt: this.props.cancelledAt,
    };
  }
}
