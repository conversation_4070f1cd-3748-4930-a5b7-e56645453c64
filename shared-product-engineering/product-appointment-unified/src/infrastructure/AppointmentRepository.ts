/**
 * 🎯 APPOINTMENT REPOSITORY - Infrastructure Layer
 */

import type { AppointmentEntity } from '../domain/AppointmentEntity';
import type {
  Appointment,
  AppointmentQuery,
  CreateAppointmentRequest,
  UpdateAppointmentRequest,
} from '../schema/appointment-schema';

// ============================================================================
// 🎯 REPOSITORY INTERFACE
// ============================================================================

export interface IAppointmentRepository {
  // Basic CRUD operations
  findById(id: string): Promise<AppointmentEntity | null>;
  findByIds(ids: string[]): Promise<AppointmentEntity[]>;
  save(appointment: AppointmentEntity): Promise<void>;
  delete(id: string): Promise<void>;

  // Query operations
  findMany(query: AppointmentQuery): Promise<AppointmentEntity[]>;
  findBySalonId(salonId: string, limit?: number): Promise<AppointmentEntity[]>;
  findByCustomerId(
    customerId: string,
    limit?: number,
  ): Promise<AppointmentEntity[]>;
  findByStaffId(staffId: string, limit?: number): Promise<AppointmentEntity[]>;
  findByDateRange(
    startDate: Date,
    endDate: Date,
    salonId?: string,
  ): Promise<AppointmentEntity[]>;
  findByStatus(status: string, salonId?: string): Promise<AppointmentEntity[]>;

  // Business operations
  findConflictingAppointments(
    staffId: string,
    startTime: Date,
    endTime: Date,
    excludeId?: string,
  ): Promise<AppointmentEntity[]>;

  findUpcomingAppointments(
    salonId: string,
    hoursAhead?: number,
  ): Promise<AppointmentEntity[]>;

  // Statistics
  countByStatus(salonId: string): Promise<Record<string, number>>;
  getAppointmentStats(
    salonId: string,
    dateRange?: { start: Date; end: Date },
  ): Promise<{
    total: number;
    completed: number;
    cancelled: number;
    noShow: number;
    revenue: number;
  }>;
}

// ============================================================================
// 🎯 QUERY BUILDERS
// ============================================================================

export interface AppointmentFilters {
  salonId?: string;
  customerId?: string;
  staffId?: string;
  treatmentId?: string;
  status?: string | string[];
  startDate?: Date;
  endDate?: Date;
  source?: string;
  limit?: number;
  offset?: number;
  orderBy?: 'startTime' | 'createdAt' | 'updatedAt';
  orderDirection?: 'asc' | 'desc';
}

export interface TimeSlot {
  startTime: Date;
  endTime: Date;
}

// ============================================================================
// 🎯 ADAPTER INTERFACES
// ============================================================================

export interface AppointmentAdapter {
  toDomain(data: any): AppointmentEntity;
  fromDomain(entity: AppointmentEntity): any;
  toResponse(entity: AppointmentEntity): Appointment;
}

// ============================================================================
// 🎯 FACTORY INTERFACE
// ============================================================================

export interface IAppointmentFactory {
  create(data: CreateAppointmentRequest): AppointmentEntity;
  reconstitute(data: Appointment): AppointmentEntity;
}

// ============================================================================
// 🎯 SERVICE INTERFACES
// ============================================================================

export interface IAppointmentDomainService {
  validateAppointmentTime(
    startTime: Date,
    endTime: Date,
    treatmentDuration: number,
  ): boolean;

  checkStaffAvailability(
    staffId: string,
    startTime: Date,
    endTime: Date,
    excludeAppointmentId?: string,
  ): Promise<boolean>;

  calculateEndTime(startTime: Date, treatmentDuration: number): Date;

  findNextAvailableSlot(
    staffId: string,
    treatmentDuration: number,
    preferredDate: Date,
  ): Promise<TimeSlot | null>;

  validateBusinessRules(appointment: AppointmentEntity): Promise<string[]>;
}

// ============================================================================
// 🎯 EVENT PUBLISHER INTERFACE
// ============================================================================

export interface IAppointmentEventPublisher {
  publishCreated(appointment: AppointmentEntity, metadata?: any): Promise<void>;
  publishUpdated(
    appointment: AppointmentEntity,
    changes: Partial<Appointment>,
    previousValues?: Partial<Appointment>,
    metadata?: any,
  ): Promise<void>;
  publishConfirmed(
    appointmentId: string,
    data: any,
    metadata?: any,
  ): Promise<void>;
  publishCancelled(
    appointmentId: string,
    data: any,
    metadata?: any,
  ): Promise<void>;
  publishCompleted(
    appointmentId: string,
    data: any,
    metadata?: any,
  ): Promise<void>;
  publishRescheduled(
    appointmentId: string,
    data: any,
    metadata?: any,
  ): Promise<void>;
  publishNoShow(
    appointmentId: string,
    data: any,
    metadata?: any,
  ): Promise<void>;
  publishStarted(
    appointmentId: string,
    data: any,
    metadata?: any,
  ): Promise<void>;
}

// ============================================================================
// 🎯 APPLICATION SERVICE INTERFACE
// ============================================================================

export interface IAppointmentApplicationService {
  // Command operations
  createAppointment(request: CreateAppointmentRequest): Promise<string>;
  updateAppointment(
    id: string,
    request: UpdateAppointmentRequest,
  ): Promise<void>;
  confirmAppointment(id: string, confirmedBy?: string): Promise<void>;
  cancelAppointment(
    id: string,
    reason?: string,
    cancelledBy?: string,
  ): Promise<void>;
  completeAppointment(
    id: string,
    notes?: string,
    completedBy?: string,
  ): Promise<void>;
  rescheduleAppointment(
    id: string,
    newStartTime: Date,
    newEndTime: Date,
    reason?: string,
  ): Promise<void>;
  markNoShow(id: string, markedBy?: string): Promise<void>;
  startAppointment(id: string, startedBy?: string): Promise<void>;

  // Query operations
  getAppointment(id: string): Promise<Appointment | null>;
  getAppointments(filters: AppointmentFilters): Promise<Appointment[]>;
  getAppointmentsByDateRange(
    startDate: Date,
    endDate: Date,
    salonId?: string,
  ): Promise<Appointment[]>;
  getUpcomingAppointments(
    salonId: string,
    hoursAhead?: number,
  ): Promise<Appointment[]>;
  getAppointmentStats(
    salonId: string,
    dateRange?: { start: Date; end: Date },
  ): Promise<any>;

  // Business operations
  checkAvailability(
    staffId: string,
    startTime: Date,
    endTime: Date,
  ): Promise<boolean>;
  findAvailableSlots(
    staffId: string,
    treatmentDuration: number,
    date: Date,
  ): Promise<TimeSlot[]>;
}
