/**
 * 🏗️ BASE ENTITY - Domain-Driven Design Foundation
 *
 * Following <PERSON><PERSON><PERSON>'s approach to DDD entities with TypeScript generics.
 * This provides the foundation for all domain entities across the system.
 */

import 'reflect-metadata';
import {
  IsString,
  IsDate,
  validateSync,
  type ValidationError,
} from 'class-validator';
import { Type } from 'class-transformer';

// ============================================================================
// 🎯 ENTITY IDENTIFIER
// ============================================================================

export class UniqueEntityID {
  private value: string;

  constructor(id?: string) {
    this.value = id || this.generateId();
  }

  equals(id?: UniqueEntityID): boolean {
    if (id === null || id === undefined) {
      return false;
    }
    if (!(id instanceof this.constructor)) {
      return false;
    }
    return id.toValue() === this.value;
  }

  toString(): string {
    return String(this.value);
  }

  toValue(): string {
    return this.value;
  }

  private generateId(): string {
    // Using crypto.randomUUID() for better uniqueness
    return crypto.randomUUID();
  }
}

// ============================================================================
// 🏗️ BASE ENTITY INTERFACE
// ============================================================================

export interface IBaseEntityProps {
  id?: string;
  createdAt?: Date;
  updatedAt?: Date;
}

// ============================================================================
// 🔥 ABSTRACT BASE ENTITY
// ============================================================================

export abstract class BaseEntity<T extends IBaseEntityProps> {
  @IsString()
  protected readonly _id: UniqueEntityID;

  @IsDate()
  @Type(() => Date)
  protected _createdAt: Date;

  @IsDate()
  @Type(() => Date)
  protected _updatedAt: Date;

  protected props: T;

  constructor(props: T, id?: UniqueEntityID) {
    this._id = id || new UniqueEntityID(props.id);
    this._createdAt = props.createdAt || new Date();
    this._updatedAt = props.updatedAt || new Date();
    this.props = props;
  }

  // ============================================================================
  // 🎯 GETTERS
  // ============================================================================

  get id(): UniqueEntityID {
    return this._id;
  }

  get createdAt(): Date {
    return this._createdAt;
  }

  get updatedAt(): Date {
    return this._updatedAt;
  }

  // ============================================================================
  // 🔄 EQUALITY & COMPARISON
  // ============================================================================

  public equals(object?: BaseEntity<T>): boolean {
    if (object === null || object === undefined) {
      return false;
    }

    if (this === object) {
      return true;
    }

    if (!(object instanceof BaseEntity)) {
      return false;
    }

    return this._id.equals(object._id);
  }

  // ============================================================================
  // 🎯 VALIDATION
  // ============================================================================

  /**
   * Validates the entity using class-validator
   */
  public validate(): ValidationError[] {
    return validateSync(this);
  }

  /**
   * Checks if the entity is valid
   */
  public isValid(): boolean {
    return this.validate().length === 0;
  }

  /**
   * Validates and throws if invalid
   */
  public validateOrThrow(): void {
    const errors = this.validate();
    if (errors.length > 0) {
      throw new Error(
        `Entity validation failed: ${errors.map((e) => e.toString()).join(', ')}`,
      );
    }
  }

  // ============================================================================
  // 🔄 LIFECYCLE METHODS
  // ============================================================================

  /**
   * Marks the entity as updated
   */
  protected markAsUpdated(): void {
    this._updatedAt = new Date();
  }

  /**
   * Updates the entity props and marks as updated
   */
  protected updateProps(updates: Partial<T>): void {
    this.props = { ...this.props, ...updates };
    this.markAsUpdated();
  }

  // ============================================================================
  // 🎯 SERIALIZATION
  // ============================================================================

  /**
   * Converts entity to plain object for serialization
   */
  public toObject(): T & { id: string; createdAt: Date; updatedAt: Date } {
    return {
      ...this.props,
      id: this._id.toValue(),
      createdAt: this._createdAt,
      updatedAt: this._updatedAt,
    };
  }

  /**
   * Creates a copy of the entity
   */
  public abstract clone(): BaseEntity<T>;
}

// ============================================================================
// 🎯 UTILITY TYPES
// ============================================================================

export type EntityID = string | number | UniqueEntityID;

export interface IEntity {
  equals(object: IEntity): boolean;
}

// ============================================================================
// 🔥 ENTITY FACTORY
// ============================================================================

export abstract class EntityFactory<
  TEntity extends BaseEntity<any>,
  TProps extends IBaseEntityProps,
> {
  /**
   * Creates a new entity instance
   */
  public abstract create(props: TProps, id?: UniqueEntityID): TEntity;

  /**
   * Recreates an entity from persisted data
   */
  public abstract reconstitute(
    props: TProps & { id: string; createdAt: Date; updatedAt: Date },
  ): TEntity;
}

// ============================================================================
// 🎯 DOMAIN EVENT SUPPORT
// ============================================================================

export interface IDomainEvent {
  dateTimeOccurred: Date;
  getAggregateId(): UniqueEntityID;
}

export abstract class AggregateRoot<
  T extends IBaseEntityProps,
> extends BaseEntity<T> {
  private _domainEvents: IDomainEvent[] = [];

  get domainEvents(): IDomainEvent[] {
    return this._domainEvents;
  }

  protected addDomainEvent(domainEvent: IDomainEvent): void {
    this._domainEvents.push(domainEvent);
  }

  public clearEvents(): void {
    this._domainEvents.splice(0, this._domainEvents.length);
  }
}

/**
 * 🏗️ BASE ENTITY - DDD Foundation
 *
 * Following Khalil Stemmler's DDD approach with generics
 * https://khalilstemmler.com/articles/typescript-domain-driven-design/entities/
 */

import { createId } from '@paralleldrive/cuid2';

// ============================================================================
// 🔥 UNIQUE ENTITY ID
// ============================================================================

export class UniqueEntityID {
  private value: string;

  constructor(id?: string) {
    this.value = id || createId();
  }

  equals(id?: UniqueEntityID): boolean {
    if (id === null || id === undefined) {
      return false;
    }
    if (!(id instanceof this.constructor)) {
      return false;
    }
    return id.toValue() === this.value;
  }

  toString(): string {
    return String(this.value);
  }

  toValue(): string {
    return this.value;
  }
}

// ============================================================================
// 🏗️ BASE ENTITY
// ============================================================================

export abstract class Entity<T> {
  protected readonly _id: UniqueEntityID;
  protected props: T;

  constructor(props: T, id?: UniqueEntityID) {
    this._id = id || new UniqueEntityID();
    this.props = props;
  }

  public equals(object?: Entity<T>): boolean {
    if (object === null || object === undefined) {
      return false;
    }

    if (this === object) {
      return true;
    }

    if (!(object instanceof Entity)) {
      return false;
    }

    return this._id.equals(object._id);
  }

  get id(): UniqueEntityID {
    return this._id;
  }

  get idValue(): string {
    return this._id.toValue();
  }
}

// ============================================================================
// 🎯 AGGREGATE ROOT
// ============================================================================

export abstract class AggregateRoot<T> extends Entity<T> {
  private _domainEvents: IDomainEvent[] = [];

  get domainEvents(): IDomainEvent[] {
    return this._domainEvents;
  }

  protected addDomainEvent(domainEvent: IDomainEvent): void {
    this._domainEvents.push(domainEvent);
  }

  public clearEvents(): void {
    this._domainEvents.splice(0, this._domainEvents.length);
  }
}

// ============================================================================
// 🎯 DOMAIN EVENT INTERFACE
// ============================================================================

export interface IDomainEvent {
  dateTimeOccurred: Date;
  getAggregateId(): UniqueEntityID;
}

// ============================================================================
// 🔥 VALUE OBJECT BASE
// ============================================================================

export abstract class ValueObject<T> {
  protected props: T;

  constructor(props: T) {
    this.props = Object.freeze(props);
  }

  public equals(vo?: ValueObject<T>): boolean {
    if (vo === null || vo === undefined) {
      return false;
    }
    if (vo.props === undefined) {
      return false;
    }
    return JSON.stringify(this.props) === JSON.stringify(vo.props);
  }
}

// ============================================================================
// 🎯 RESULT PATTERN
// ============================================================================

export class Result<T> {
  public isSuccess: boolean;
  public isFailure: boolean;
  public error?: string;
  private _value?: T;

  public constructor(isSuccess: boolean, error?: string, value?: T) {
    if (isSuccess && error) {
      throw new Error(
        'InvalidOperation: A result cannot be successful and contain an error',
      );
    }
    if (!isSuccess && !error) {
      throw new Error(
        'InvalidOperation: A failing result needs to contain an error message',
      );
    }

    this.isSuccess = isSuccess;
    this.isFailure = !isSuccess;
    this.error = error;
    this._value = value;

    Object.freeze(this);
  }

  public getValue(): T {
    if (!this.isSuccess) {
      throw new Error(
        "Can't get the value of an error result. Use 'errorValue' instead.",
      );
    }

    return this._value as T;
  }

  public static ok<U>(value?: U): Result<U> {
    return new Result<U>(true, undefined, value);
  }

  public static fail<U>(error: string): Result<U> {
    return new Result<U>(false, error);
  }

  public static combine(results: Result<any>[]): Result<any> {
    for (const result of results) {
      if (result.isFailure) return result;
    }
    return Result.ok();
  }
}

// ============================================================================
// 🎯 GUARD CLAUSES
// ============================================================================

export interface IGuardResult {
  succeeded: boolean;
  message?: string;
}

export interface IGuardArgument {
  argument: any;
  argumentName: string;
}

export type GuardArgumentCollection = IGuardArgument[];

export class Guard {
  public static combine(guardResults: IGuardResult[]): IGuardResult {
    for (const result of guardResults) {
      if (result.succeeded === false) return result;
    }

    return { succeeded: true };
  }

  public static greaterThan(
    minValue: number,
    actualValue: number,
  ): IGuardResult {
    return actualValue > minValue
      ? { succeeded: true }
      : {
          succeeded: false,
          message: `Number given {${actualValue}} is not greater than {${minValue}}`,
        };
  }

  public static againstAtLeast(numChars: number, text: string): IGuardResult {
    return text.length >= numChars
      ? { succeeded: true }
      : {
          succeeded: false,
          message: `Text is not at least ${numChars} chars.`,
        };
  }

  public static againstAtMost(numChars: number, text: string): IGuardResult {
    return text.length <= numChars
      ? { succeeded: true }
      : {
          succeeded: false,
          message: `Text is greater than ${numChars} chars.`,
        };
  }

  public static againstNullOrUndefined(
    argument: any,
    argumentName: string,
  ): IGuardResult {
    if (argument === null || argument === undefined) {
      return {
        succeeded: false,
        message: `${argumentName} is null or undefined`,
      };
    }
    return { succeeded: true };
  }

  public static againstNullOrUndefinedBulk(
    args: GuardArgumentCollection,
  ): IGuardResult {
    for (const arg of args) {
      const result = Guard.againstNullOrUndefined(
        arg.argument,
        arg.argumentName,
      );
      if (!result.succeeded) return result;
    }

    return { succeeded: true };
  }

  public static isOneOf(
    value: any,
    validValues: any[],
    argumentName: string,
  ): IGuardResult {
    let isValid = false;
    for (const validValue of validValues) {
      if (value === validValue) {
        isValid = true;
      }
    }

    if (isValid) {
      return { succeeded: true };
    }
    return {
      succeeded: false,
      message: `${argumentName} isn't oneOf the correct types in ${JSON.stringify(validValues)}. Got "${value}".`,
    };
  }

  public static inRange(
    num: number,
    min: number,
    max: number,
    argumentName: string,
  ): IGuardResult {
    const isInRange = num >= min && num <= max;
    if (!isInRange) {
      return {
        succeeded: false,
        message: `${argumentName} is not within range ${min} to ${max}.`,
      };
    }
    return { succeeded: true };
  }

  public static allInRange(
    numbers: number[],
    min: number,
    max: number,
    argumentName: string,
  ): IGuardResult {
    let failingResult: IGuardResult | null = null;
    for (let i = 0; i < numbers.length; i++) {
      const numIsInRangeResult = Guard.inRange(
        numbers[i],
        min,
        max,
        argumentName,
      );
      if (!numIsInRangeResult.succeeded) failingResult = numIsInRangeResult;
    }

    if (failingResult) {
      return {
        succeeded: false,
        message: `${argumentName} is not within the range.`,
      };
    }
    return { succeeded: true };
  }
}
